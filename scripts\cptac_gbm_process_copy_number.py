import pandas as pd
import json

# --------- USER CONFIGURATION ---------
CN_FILE = "data/cptac_gbm/raw_data/copy_number/cptac_gbm_wgs_somatic_cnv_per_gene.cct"
GEO_META_FILE = "geo_response_metadata.csv"         # Optional: responder labels
WSI_JSON_PATH = r"study_patient_mapping.json"      # JSON file as described
OUTPUT_FILE = "data/cptac_gbm/processed_data/cptac_gbm_copy_number_processed.csv"

# Immune-related genes for copy number analysis
IMMUNE_GENES = [
    # Immune checkpoint genes
    "CD274",    # PD-L1 (9p24.1)
    "PDCD1",    # PD-1 (2q37.3)
    "CTLA4",    # CTLA-4 (2q33.2)
    "LAG3",     # LAG-3 (12p13.32)
    "TIGIT",    # TIGIT (3q13.31)
    "HAVCR2",   # TIM-3 (5q33.3)
    "BTLA",     # BTLA (3q13.2)
    "ICOS",     # ICOS (2q33.3)
    "ICOSLG",   # ICOS-L (21q22.3)
    
    # T cell function genes
    "CD8A",     # CD8A (2p11.2)
    "CD8B",     # CD8B (2p11.2)
    "CD4",      # CD4 (12p13.31)
    "FOXP3",    # FOXP3 (Xp11.23)
    "GZMB",     # Granzyme B (14q12)
    "PRF1",     # Perforin 1 (10q22.1)
    "TBX21",    # T-bet (17q21.32)
    "RORC",     # RORγt (1q21.3)
    
    # Cytokine genes
    "IFNG",     # IFN-γ (12q15)
    "TNF",      # TNF-α (6p21.33)
    "IL2",      # IL-2 (4q27)
    "IL6",      # IL-6 (7p15.3)
    "IL10",     # IL-10 (1q32.1)
    "IL12A",    # IL-12A (3q25.33)
    "IL12B",    # IL-12B (5q33.3)
    "IL17A",    # IL-17A (6p12.2)
    "IL4",      # IL-4 (5q31.1)
    
    # Chemokine genes
    "CXCL9",    # CXCL9 (4q21.1)
    "CXCL10",   # CXCL10 (4q21.1)
    "CCL2",     # CCL2/MCP-1 (17q12)
    "CCL5",     # CCL5/RANTES (17q12)
    
    # Antigen presentation genes
    "HLA-A",    # HLA-A (6p21.33)
    "HLA-B",    # HLA-B (6p21.33)
    "HLA-C",    # HLA-C (6p21.33)
    "HLA-DRA",  # HLA-DRA (6p21.32)
    "HLA-DRB1", # HLA-DRB1 (6p21.32)
    "B2M",      # Beta-2 microglobulin (15q21.1)
    "TAP1",     # TAP1 (6p21.32)
    "TAP2",     # TAP2 (6p21.32)
    
    # Transcription factors
    "STAT1",    # STAT1 (2q32.2)
    "STAT3",    # STAT3 (17q21.2)
    "STAT4",    # STAT4 (2q32.2)
    "STAT6",    # STAT6 (12q13.3)
    "IRF1",     # IRF1 (5q31.1)
    "IRF4",     # IRF4 (6p25.3)
    "IRF8",     # IRF8 (16q24.1)
    
    # Immune cell markers
    "CD68",     # CD68 (17p13.1)
    "CD163",    # CD163 (12p13.31)
    "NCAM1",    # NCAM1/CD56 (11q23.2)
    "KLRK1",    # NKG2D (12p13.2)
]
# --------------------------------------

# --------- 1. LOAD COPY NUMBER DATA ---------
print("Loading CPTAC-GBM copy number data...")
cn_df = pd.read_csv(CN_FILE, sep="\t", index_col=0)

print(f"Loaded copy number data: {cn_df.shape}")
print(f"Sample IDs (first 5): {list(cn_df.columns[:5])}")

# Transpose so rows = samples, columns = genes
cn_df = cn_df.T
cn_df.index.name = "case_id"

print(f"Transposed data: {cn_df.shape}")

# --------- 2. FILTER FOR IMMUNE GENES ---------
print("Filtering immune-related genes...")

# Find immune genes that exist in the dataset
available_genes = [gene for gene in IMMUNE_GENES if gene in cn_df.columns]
missing_genes = [gene for gene in IMMUNE_GENES if gene not in cn_df.columns]

print(f"Available immune genes: {len(available_genes)}")
print(f"Missing immune genes: {len(missing_genes)}")

if available_genes:
    immune_cn_df = cn_df[available_genes].copy()
    print(f"Available genes: {available_genes}")
else:
    print("WARNING: No immune genes found. Using top variable genes instead.")
    
    # Calculate variance for each gene and select top variable ones
    gene_var = cn_df.var(axis=0).sort_values(ascending=False)
    
    # Look for immune-related genes in top variable genes
    immune_keywords = ['CD', 'IL', 'TNF', 'IFNG', 'HLA', 'STAT', 'CXCL', 'CCL', 'GZMB', 'PRF1', 'FOXP3', 'IRF']
    immune_genes = []
    
    for gene in gene_var.index[:1000]:  # Check top 1000 variable genes
        if any(keyword in gene.upper() for keyword in immune_keywords):
            immune_genes.append(gene)
        if len(immune_genes) >= 50:  # Limit to top 50 immune-related genes
            break
    
    if immune_genes:
        immune_cn_df = cn_df[immune_genes].copy()
        print(f"Selected {len(immune_genes)} immune-related genes from variable genes")
    else:
        # Fallback: use top 30 most variable genes
        top_genes = gene_var.head(30).index.tolist()
        immune_cn_df = cn_df[top_genes].copy()
        print(f"Fallback: Using top 30 most variable genes")

print(f"Final immune gene set: {immune_cn_df.shape}")

# --------- 3. PROCESS COPY NUMBER VALUES ---------
print("Processing copy number values...")

# Copy number values are log2 ratios (BIC-seq2)
# Typical interpretation:
# > 0.3: Amplification
# -0.3 to 0.3: Normal
# < -0.3: Deletion

numeric_cols = [col for col in immune_cn_df.columns]

# Create binary features for significant alterations
for col in numeric_cols:
    immune_cn_df[f"{col}_amplified"] = (immune_cn_df[col] > 0.3).astype(int)
    immune_cn_df[f"{col}_deleted"] = (immune_cn_df[col] < -0.3).astype(int)
    immune_cn_df[f"{col}_altered"] = ((immune_cn_df[col] > 0.3) | (immune_cn_df[col] < -0.3)).astype(int)

print("Created binary alteration features")

# --------- 4. OPTIONAL: ADD RESPONSE LABELS ---------
print("Adding response labels...")
immune_cn_df['response'] = 'Unknown'  # Default response

try:
    geo_df = pd.read_csv(GEO_META_FILE, index_col=0)
    print(f"Loaded GEO metadata: {geo_df.shape}")
    
    # Map response labels
    for case_id in immune_cn_df.index:
        if case_id in geo_df.index:
            immune_cn_df.loc[case_id, 'response'] = geo_df.loc[case_id, 'response']
    
    response_counts = immune_cn_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")
    
except FileNotFoundError:
    print("GEO metadata file not found. Creating pseudo-response labels.")
    
    # Create pseudo-response based on copy number alteration burden
    alteration_cols = [col for col in immune_cn_df.columns if col.endswith('_altered')]
    
    if alteration_cols:
        # Calculate total alteration burden
        alteration_burden = immune_cn_df[alteration_cols].sum(axis=1)
        
        # Classify based on alteration burden tertiles
        q33 = alteration_burden.quantile(0.33)
        q67 = alteration_burden.quantile(0.67)
        
        # Higher alteration burden might indicate worse prognosis
        immune_cn_df.loc[alteration_burden >= q67, 'response'] = 'Poor'
        immune_cn_df.loc[(alteration_burden >= q33) & (alteration_burden < q67), 'response'] = 'Intermediate'
        immune_cn_df.loc[alteration_burden < q33, 'response'] = 'Good'
        
        print("Created pseudo-response labels based on copy number alteration burden")

except Exception as e:
    print(f"Error processing response labels: {e}")

# --------- 5. ADD WSI METADATA ---------
print("Adding WSI metadata...")
immune_cn_df['num_patches'] = 'No WSI Data'
immune_cn_df['file'] = 'No WSI Data'

try:
    with open(WSI_JSON_PATH, 'r') as f:
        wsi_mapping = json.load(f)
    
    for case_id in immune_cn_df.index:
        if case_id in wsi_mapping:
            immune_cn_df.loc[case_id, 'num_patches'] = wsi_mapping[case_id].get('num_patches', 'No WSI Data')
            immune_cn_df.loc[case_id, 'file'] = wsi_mapping[case_id].get('file', 'No WSI Data')
    
    wsi_available = (immune_cn_df['num_patches'] != 'No WSI Data').sum()
    print(f"WSI data available for {wsi_available}/{len(immune_cn_df)} samples")
    
except FileNotFoundError:
    print("WSI mapping file not found. Using placeholder values.")
except Exception as e:
    print(f"Error loading WSI mapping: {e}")

# --------- 6. CALCULATE COPY NUMBER-BASED SCORES ---------
print("Calculating copy number-based immune scores...")

# Get original numeric columns (log2 ratios)
original_numeric_cols = [col for col in immune_cn_df.columns 
                        if not col.endswith(('_amplified', '_deleted', '_altered')) 
                        and col not in ['response', 'num_patches', 'file']]

if original_numeric_cols:
    # Immune checkpoint alteration score
    checkpoint_genes = [col for col in original_numeric_cols if col in ['CD274', 'PDCD1', 'CTLA4', 'LAG3', 'TIGIT', 'HAVCR2']]
    if checkpoint_genes:
        checkpoint_altered_cols = [f"{gene}_altered" for gene in checkpoint_genes]
        immune_cn_df['checkpoint_alteration_score'] = immune_cn_df[checkpoint_altered_cols].sum(axis=1)
    
    # T cell function alteration score
    tcell_genes = [col for col in original_numeric_cols if col in ['CD8A', 'CD8B', 'CD4', 'GZMB', 'PRF1', 'FOXP3']]
    if tcell_genes:
        tcell_altered_cols = [f"{gene}_altered" for gene in tcell_genes]
        immune_cn_df['tcell_alteration_score'] = immune_cn_df[tcell_altered_cols].sum(axis=1)
    
    # Antigen presentation alteration score
    mhc_genes = [col for col in original_numeric_cols if col in ['HLA-A', 'HLA-B', 'HLA-C', 'HLA-DRA', 'HLA-DRB1', 'B2M', 'TAP1', 'TAP2']]
    if mhc_genes:
        mhc_altered_cols = [f"{gene}_altered" for gene in mhc_genes]
        immune_cn_df['mhc_alteration_score'] = immune_cn_df[mhc_altered_cols].sum(axis=1)
    
    # Overall immune alteration burden
    all_altered_cols = [col for col in immune_cn_df.columns if col.endswith('_altered')]
    if all_altered_cols:
        immune_cn_df['total_immune_alteration_burden'] = immune_cn_df[all_altered_cols].sum(axis=1)
    
    # Copy number immune score (based on log2 ratios)
    immune_cn_df['copy_number_immune_score'] = immune_cn_df[original_numeric_cols].mean(axis=1)
    
    print("Calculated copy number-based immune scores")

# --------- 7. SAVE PROCESSED DATA ---------
print("Saving processed copy number data...")
immune_cn_df.to_csv(OUTPUT_FILE)

print(f"SUCCESS: Processed copy number data saved to: {OUTPUT_FILE}")
print(f"Final dataset shape: {immune_cn_df.shape}")

# --------- 8. SUMMARY STATISTICS ---------
print("\n" + "="*50)
print("CPTAC-GBM COPY NUMBER DATA SUMMARY")
print("="*50)

print(f"Total samples: {len(immune_cn_df)}")
print(f"Total genes/features: {len(original_numeric_cols) if 'original_numeric_cols' in locals() else 'N/A'}")

if 'response' in immune_cn_df.columns:
    response_counts = immune_cn_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")

# Show copy number statistics for key immune genes
key_genes = ['CD274', 'PDCD1', 'CTLA4', 'CD8A', 'FOXP3', 'GZMB']
available_key_genes = [gene for gene in key_genes if gene in immune_cn_df.columns]

if available_key_genes:
    print("\nKey immune gene copy number (log2 ratio):")
    for gene in available_key_genes:
        mean_cn = immune_cn_df[gene].mean()
        std_cn = immune_cn_df[gene].std()
        amplified = (immune_cn_df[gene] > 0.3).sum()
        deleted = (immune_cn_df[gene] < -0.3).sum()
        print(f"  {gene}: {mean_cn:.3f} ± {std_cn:.3f} (Amp: {amplified}, Del: {deleted})")

# Show copy number alteration frequencies
if 'total_immune_alteration_burden' in immune_cn_df.columns:
    mean_burden = immune_cn_df['total_immune_alteration_burden'].mean()
    print(f"\nMean immune alteration burden: {mean_burden:.1f} genes per sample")

# Show copy number-based scores if calculated
score_cols = [col for col in immune_cn_df.columns if 'score' in col.lower() or 'burden' in col.lower()]
if score_cols:
    print("\nCopy number-based immune scores:")
    for score in score_cols:
        mean_score = immune_cn_df[score].mean()
        std_score = immune_cn_df[score].std()
        print(f"  {score}: {mean_score:.2f} ± {std_score:.2f}")

wsi_available = (immune_cn_df['num_patches'] != 'No WSI Data').sum()
print(f"\nWSI data availability: {wsi_available}/{len(immune_cn_df)} samples ({wsi_available/len(immune_cn_df)*100:.1f}%)")

print("="*50)
