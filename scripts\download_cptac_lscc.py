#!/usr/bin/env python3
"""
CPTAC-LSCC Dataset Downloader

Downloads the complete CPTAC Lung Squamous Cell Carcinoma dataset from LinkedOmics.

Author: AI Assistant
Date: 2025-01-02
"""

import requests
import os
from pathlib import Path
import logging
from tqdm import tqdm
import time

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# CPTAC-LSCC file configuration
CPTAC_LSCC_FILES = {
    'clinical_tumor': 'HS_CPTAC_LSCC_2020_clinical_phenotypes_TUMOR.tsi',
    'clinical_normal': 'HS_CPTAC_LSCC_2020_clinical_phenotypes_NORMAL.tsi',
    'molecular_tumor': 'HS_CPTAC_LSCC_2020_molecular_phenotypes_TUMOR.tsi',
    'molecular_normal': 'HS_CPTAC_LSCC_2020_molecular_phenotypes_NORMAL.tsi',
    'rnaseq_tumor': 'HS_CPTAC_LSCC_2020_rnaseq_uq_fpkm_log2_NArm_TUMOR.cct',
    'rnaseq_normal': 'HS_CPTAC_LSCC_2020_rnaseq_uq_fpkm_log2_NArm_NORMAL.cct',
    'proteome_tumor': 'HS_CPTAC_LSCC_2020_proteome_ratio_NArm_TUMOR.cct',
    'proteome_normal': 'HS_CPTAC_LSCC_2020_proteome_ratio_NArm_NORMAL.cct',
    'phosphoproteome_tumor': 'HS_CPTAC_LSCC_2020_phospho_ratio_norm_NArm_TUMOR.cct',
    'phosphoproteome_normal': 'HS_CPTAC_LSCC_2020_phospho_ratio_norm_NArm_NORMAL.cct',
    'acetylome_tumor': 'HS_CPTAC_LSCC_2020_acetyl_ratio_norm_NArm_TUMOR.cct',
    'acetylome_normal': 'HS_CPTAC_LSCC_2020_acetyl_ratio_norm_NArm_NORMAL.cct',
    'ubiquitin_tumor': 'HS_CPTAC_LSCC_2020_ubiquitin_ratio_norm_NArm_TUMOR.cct',
    'ubiquitin_normal': 'HS_CPTAC_LSCC_2020_ubiquitin_ratio_norm_NArm_NORMAL.cct',
    'methylation_tumor': 'HS_CPTAC_LSCC_2020_methylation_mean_gene_tumor.cct',
    'methylation_normal': 'HS_CPTAC_LSCC_2020_methylation_mean_gene_normal.cct',
    'copy_number': 'HS_CPTAC_LSCC_2020_cnv_gene_gistic2_log_ratio.cct',
    'mirna_tumor': 'HS_CPTAC_LSCC_2020_miRNA_tpm_log2_NArm_TUMOR.cct',
    'mirna_normal': 'HS_CPTAC_LSCC_2020_miRNA_tpm_log2_NArm_NORMAL.cct',
    'circular_rna_tumor': 'HS_CPTAC_LSCC_2020_ciRNA_rsem_uq_log2_NArm_TUMOR.cct',
    'circular_rna_normal': 'HS_CPTAC_LSCC_2020_ciRNA_rsem_uq_log2_NArm_NORMAL.cct',
    'mutation_gene': 'HS_CPTAC_LSCC_2020_somatic_mutation_gene.cbt'
}

BASE_URL = "https://linkedomics.org/data_download/CPTAC-LSCC/"

def download_file(url: str, local_path: Path, max_retries: int = 3) -> bool:
    """Download a file with progress bar and retry logic"""
    
    for attempt in range(max_retries):
        try:
            logger.info(f"Downloading {local_path.name} (attempt {attempt + 1}/{max_retries})")
            
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            # Get file size for progress bar
            total_size = int(response.headers.get('content-length', 0))
            
            with open(local_path, 'wb') as f:
                with tqdm(total=total_size, unit='B', unit_scale=True, desc=local_path.name) as pbar:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            pbar.update(len(chunk))
            
            # Verify file was downloaded
            if local_path.exists() and local_path.stat().st_size > 0:
                file_size_mb = local_path.stat().st_size / (1024 * 1024)
                logger.info(f"Successfully downloaded {local_path.name} ({file_size_mb:.1f} MB)")
                return True
            else:
                logger.error(f"Downloaded file {local_path.name} is empty or doesn't exist")
                
        except requests.exceptions.RequestException as e:
            logger.error(f"Download failed for {local_path.name}: {e}")
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.info(f"Retrying in {wait_time} seconds...")
                time.sleep(wait_time)
            else:
                logger.error(f"Failed to download {local_path.name} after {max_retries} attempts")
                return False
        
        except Exception as e:
            logger.error(f"Unexpected error downloading {local_path.name}: {e}")
            return False
    
    return False

def main():
    """Main download function"""
    logger.info("Starting CPTAC-LSCC dataset download...")
    
    # Create directory structure
    base_dir = Path("data/cptac_lscc")
    raw_data_dir = base_dir / "raw_data"
    processed_data_dir = base_dir / "processed_data"
    
    raw_data_dir.mkdir(parents=True, exist_ok=True)
    processed_data_dir.mkdir(parents=True, exist_ok=True)
    
    logger.info(f"Created directory structure: {base_dir}")
    
    # Download all files
    download_results = {}
    total_size = 0
    successful_downloads = 0
    
    logger.info(f"Downloading {len(CPTAC_LSCC_FILES)} files...")
    
    for file_type, filename in CPTAC_LSCC_FILES.items():
        url = BASE_URL + filename
        local_path = raw_data_dir / filename
        
        logger.info(f"Processing {file_type}: {filename}")
        
        success = download_file(url, local_path)
        download_results[file_type] = {
            'filename': filename,
            'success': success,
            'size_mb': local_path.stat().st_size / (1024 * 1024) if success else 0
        }
        
        if success:
            successful_downloads += 1
            total_size += local_path.stat().st_size
    
    # Create download log
    log_file = base_dir / "download_log.txt"
    with open(log_file, 'w') as f:
        f.write("CPTAC-LSCC Dataset Download Log\n")
        f.write("=" * 40 + "\n\n")
        f.write(f"Download Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Total Files: {len(CPTAC_LSCC_FILES)}\n")
        f.write(f"Successful Downloads: {successful_downloads}\n")
        f.write(f"Failed Downloads: {len(CPTAC_LSCC_FILES) - successful_downloads}\n")
        f.write(f"Total Size: {total_size / (1024 * 1024):.1f} MB\n\n")
        
        f.write("File Details:\n")
        f.write("-" * 20 + "\n")
        
        for file_type, result in download_results.items():
            status = "SUCCESS" if result['success'] else "FAILED"
            f.write(f"{file_type}: {result['filename']} - {status}")
            if result['success']:
                f.write(f" ({result['size_mb']:.1f} MB)")
            f.write("\n")
    
    # Summary
    logger.info("CPTAC-LSCC download complete!")
    logger.info(f"Results: {successful_downloads}/{len(CPTAC_LSCC_FILES)} files downloaded successfully")
    logger.info(f"Total size: {total_size / (1024 * 1024):.1f} MB")
    logger.info(f"Download log saved: {log_file}")
    
    # List failed downloads
    failed_downloads = [file_type for file_type, result in download_results.items() if not result['success']]
    if failed_downloads:
        logger.warning("Failed downloads:")
        for file_type in failed_downloads:
            logger.warning(f"  - {file_type}: {download_results[file_type]['filename']}")
    
    return successful_downloads == len(CPTAC_LSCC_FILES)

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
