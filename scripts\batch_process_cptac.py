#!/usr/bin/env python3
"""
Batch CPTAC Multi-Dataset Processor

This script processes all available CPTAC datasets using the universal processor.

Author: AI Assistant
Date: 2025-01-02
"""

import subprocess
import sys
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Available CPTAC datasets
CPTAC_DATASETS = [
    'CPTAC-GBM',
    'CPTAC-UCEC',
    'CPTAC-LUAD',
    'CPTAC-COAD',
    'CPTAC-BRCA',
    'CPTAC-LSCC'
]

# Data types to process for each dataset
DATA_TYPES = ['clinical', 'rnaseq', 'proteome']

# Sample types to process
SAMPLE_TYPES = ['tumor', 'normal']

def run_processor(dataset_id: str, data_types: list, sample_types: list) -> bool:
    """Run the universal processor for a specific dataset"""
    logger.info(f"Processing {dataset_id}...")
    
    cmd = [
        sys.executable, 
        'scripts/universal_cptac_processor.py',
        dataset_id,
        '--data-types'] + data_types + [
        '--sample-types'] + sample_types
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        logger.info(f"Successfully processed {dataset_id}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Failed to process {dataset_id}: {e}")
        logger.error(f"STDOUT: {e.stdout}")
        logger.error(f"STDERR: {e.stderr}")
        return False

def main():
    """Main batch processing function"""
    logger.info("Starting batch CPTAC dataset processing...")
    logger.info(f"Datasets to process: {CPTAC_DATASETS}")
    logger.info(f"Data types: {DATA_TYPES}")
    logger.info(f"Sample types: {SAMPLE_TYPES}")
    
    results = {}
    
    for dataset_id in CPTAC_DATASETS:
        success = run_processor(dataset_id, DATA_TYPES, SAMPLE_TYPES)
        results[dataset_id] = success
    
    # Summary
    logger.info("Batch processing complete!")
    logger.info("Results:")
    
    successful = []
    failed = []
    
    for dataset_id, success in results.items():
        if success:
            logger.info(f"  ✓ {dataset_id}: SUCCESS")
            successful.append(dataset_id)
        else:
            logger.info(f"  ✗ {dataset_id}: FAILED")
            failed.append(dataset_id)
    
    logger.info(f"\nSummary: {len(successful)} successful, {len(failed)} failed")
    
    if successful:
        logger.info("Successfully processed datasets:")
        for dataset in successful:
            logger.info(f"  - {dataset}")
    
    if failed:
        logger.info("Failed datasets:")
        for dataset in failed:
            logger.info(f"  - {dataset}")
    
    return len(failed) == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
