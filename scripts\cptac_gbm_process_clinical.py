import pandas as pd
import json

# --------- USER CONFIGURATION ---------
CLINICAL_FILE = "data/cptac_gbm/raw_data/clinical/cptac_gbm_clinical.tsi"
WSI_JSON_PATH = r"study_patient_mapping.json"  # Placeholder for future WSI integration
OUTPUT_FILE = "data/cptac_gbm/processed_data/cptac_gbm_clinical_processed.csv"
# --------------------------------------

print("Processing CPTAC-GBM Clinical Data...")

# --------- 1. LOAD CLINICAL DATA ---------
print("Loading clinical data...")
clinical_df = pd.read_csv(CLINICAL_FILE, sep="\t", index_col=0)

# Remove the data type row (second row with NUM, BIN, CAT, etc.)
if clinical_df.index[0] == '':
    clinical_df = clinical_df.drop(clinical_df.index[0])

print(f"Loaded clinical data: {clinical_df.shape}")
print(f"Clinical variables: {list(clinical_df.columns)}")

# --------- 2. PROCESS CLINICAL VARIABLES ---------
print("Processing clinical variables...")

# Convert age to numeric
if 'age' in clinical_df.columns:
    clinical_df['age'] = pd.to_numeric(clinical_df['age'], errors='coerce')

# Convert BMI to numeric
if 'bmi' in clinical_df.columns:
    clinical_df['bmi'] = pd.to_numeric(clinical_df['bmi'], errors='coerce')

# Process gender (binary encoding)
if 'gender' in clinical_df.columns:
    clinical_df['gender_male'] = (clinical_df['gender'] == 'Male').astype(int)
    clinical_df['gender_female'] = (clinical_df['gender'] == 'Female').astype(int)

# Process smoking history
if 'smoking_history' in clinical_df.columns:
    # Create binary variables for smoking status
    clinical_df['smoking_never'] = clinical_df['smoking_history'].str.contains('Lifelong non-smoker', na=False).astype(int)
    clinical_df['smoking_current'] = clinical_df['smoking_history'].str.contains('Current smoker', na=False).astype(int)
    clinical_df['smoking_past'] = clinical_df['smoking_history'].str.contains('Past Smoker', na=False).astype(int)

# Process tumor site
if 'tumor_site_curated' in clinical_df.columns:
    # Create binary variables for major brain regions
    clinical_df['tumor_frontal'] = clinical_df['tumor_site_curated'].str.contains('Frontal', na=False).astype(int)
    clinical_df['tumor_temporal'] = clinical_df['tumor_site_curated'].str.contains('Temporal', na=False).astype(int)
    clinical_df['tumor_parietal'] = clinical_df['tumor_site_curated'].str.contains('Parietal', na=False).astype(int)
    clinical_df['tumor_occipital'] = clinical_df['tumor_site_curated'].str.contains('Occipital', na=False).astype(int)

# Process country/ethnicity
if 'country_of_origin' in clinical_df.columns:
    clinical_df['country_usa'] = (clinical_df['country_of_origin'] == 'United States').astype(int)
    clinical_df['country_russia'] = (clinical_df['country_of_origin'] == 'Russia').astype(int)
    clinical_df['country_poland'] = (clinical_df['country_of_origin'] == 'Poland').astype(int)
    clinical_df['country_mexico'] = (clinical_df['country_of_origin'] == 'Mexico').astype(int)

if 'ethnicity_self_identify' in clinical_df.columns:
    clinical_df['ethnicity_caucasian'] = (clinical_df['ethnicity_self_identify'] == 'Caucasian').astype(int)

# --------- 3. CREATE CLINICAL RISK SCORES ---------
print("Creating clinical risk scores...")

# Age-based risk (older patients typically have worse prognosis in GBM)
clinical_df['age_risk_score'] = 0
if 'age' in clinical_df.columns:
    clinical_df.loc[clinical_df['age'] >= 65, 'age_risk_score'] = 2  # High risk
    clinical_df.loc[(clinical_df['age'] >= 50) & (clinical_df['age'] < 65), 'age_risk_score'] = 1  # Medium risk
    # Age < 50 remains 0 (low risk)

# BMI-based risk (extreme BMI values may affect prognosis)
clinical_df['bmi_risk_score'] = 0
if 'bmi' in clinical_df.columns:
    clinical_df.loc[(clinical_df['bmi'] < 18.5) | (clinical_df['bmi'] > 35), 'bmi_risk_score'] = 1  # High/Low BMI risk

# Smoking risk (smoking may affect treatment response and prognosis)
clinical_df['smoking_risk_score'] = 0
if 'smoking_current' in clinical_df.columns:
    clinical_df.loc[clinical_df['smoking_current'] == 1, 'smoking_risk_score'] = 1

# Combined clinical risk score
risk_columns = ['age_risk_score', 'bmi_risk_score', 'smoking_risk_score']
existing_risk_columns = [col for col in risk_columns if col in clinical_df.columns]
if existing_risk_columns:
    clinical_df['clinical_risk_score'] = clinical_df[existing_risk_columns].sum(axis=1)

# --------- 4. OPTIONAL: ADD WSI METADATA ---------
print("Adding WSI metadata...")
clinical_df['num_patches'] = 'No WSI Data'  # Placeholder
clinical_df['file'] = 'No WSI Data'  # Placeholder

# Try to load WSI mapping if available
try:
    with open(WSI_JSON_PATH, 'r') as f:
        wsi_mapping = json.load(f)
    
    # Map WSI data to clinical samples
    for case_id in clinical_df.index:
        if case_id in wsi_mapping:
            clinical_df.loc[case_id, 'num_patches'] = wsi_mapping[case_id].get('num_patches', 'No WSI Data')
            clinical_df.loc[case_id, 'file'] = wsi_mapping[case_id].get('file', 'No WSI Data')
    
    wsi_available = (clinical_df['num_patches'] != 'No WSI Data').sum()
    print(f"WSI data available for {wsi_available}/{len(clinical_df)} samples")
    
except FileNotFoundError:
    print("WSI mapping file not found. Using placeholder values.")
except Exception as e:
    print(f"Error loading WSI mapping: {e}")

# --------- 5. ADD RESPONSE LABELS (PLACEHOLDER) ---------
print("Adding response labels...")
# For GBM, we don't have explicit response data in this dataset
# Create placeholder response based on clinical risk factors
clinical_df['response'] = 'Unknown'

# Could potentially use survival data or other clinical endpoints if available
# For now, create a risk-based pseudo-response
if 'clinical_risk_score' in clinical_df.columns:
    # High risk patients might be considered "poor responders"
    clinical_df.loc[clinical_df['clinical_risk_score'] >= 2, 'response'] = 'Poor'
    clinical_df.loc[clinical_df['clinical_risk_score'] == 1, 'response'] = 'Intermediate'
    clinical_df.loc[clinical_df['clinical_risk_score'] == 0, 'response'] = 'Good'

# --------- 6. SAVE PROCESSED DATA ---------
print("Saving processed clinical data...")
clinical_df.to_csv(OUTPUT_FILE)

print(f"SUCCESS: Processed clinical data saved to: {OUTPUT_FILE}")
print(f"Final dataset shape: {clinical_df.shape}")
print(f"Processed variables: {list(clinical_df.columns)}")

# --------- 7. SUMMARY STATISTICS ---------
print("\n" + "="*50)
print("CPTAC-GBM CLINICAL DATA SUMMARY")
print("="*50)

print(f"Total samples: {len(clinical_df)}")

if 'age' in clinical_df.columns:
    print(f"Age: {clinical_df['age'].mean():.1f} ± {clinical_df['age'].std():.1f} years")

if 'gender' in clinical_df.columns:
    gender_counts = clinical_df['gender'].value_counts()
    print(f"Gender: {gender_counts.to_dict()}")

if 'bmi' in clinical_df.columns:
    print(f"BMI: {clinical_df['bmi'].mean():.1f} ± {clinical_df['bmi'].std():.1f}")

if 'smoking_history' in clinical_df.columns:
    smoking_counts = clinical_df['smoking_history'].value_counts()
    print("Smoking history:")
    for status, count in smoking_counts.items():
        if pd.notna(status):
            print(f"  {status}: {count}")

if 'tumor_site_curated' in clinical_df.columns:
    tumor_sites = clinical_df['tumor_site_curated'].value_counts()
    print("Tumor sites:")
    for site, count in tumor_sites.head(5).items():
        if pd.notna(site):
            print(f"  {site}: {count}")

if 'country_of_origin' in clinical_df.columns:
    country_counts = clinical_df['country_of_origin'].value_counts()
    print(f"Countries: {country_counts.to_dict()}")

if 'response' in clinical_df.columns:
    response_counts = clinical_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")

print("="*50)
