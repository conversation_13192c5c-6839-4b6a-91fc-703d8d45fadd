import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import classification_report, confusion_matrix
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# --------- USER CONFIGURATION ---------
# Input files
CLINICAL_FILE = "data/cptac_gbm/processed_data/cptac_gbm_clinical_processed.csv"
RNASEQ_FILE = "data/cptac_gbm/processed_data/cptac_gbm_rnaseq_processed.csv"
MIRNA_FILE = "data/cptac_gbm/processed_data/cptac_gbm_mirna_processed.csv"
PROTEOME_FILE = "data/cptac_gbm/processed_data/cptac_gbm_proteome_processed.csv"
COPY_NUMBER_FILE = "data/cptac_gbm/processed_data/cptac_gbm_copy_number_processed.csv"

# Output files
OUTPUT_DIR = "data/cptac_gbm/processed_data/"
INTEGRATED_FILE = OUTPUT_DIR + "cptac_gbm_integrated_multiomics.csv"
FEATURES_FILE = OUTPUT_DIR + "cptac_gbm_selected_features.csv"
RESULTS_FILE = OUTPUT_DIR + "cptac_gbm_integration_results.txt"

# Analysis parameters
ENABLE_TPM_NORMALIZATION = True  # Enable TPM normalization for RNA-seq
TPM_SCALING_FACTOR = 1e6        # TPM scaling factor
FEATURE_SELECTION_TOP_K = 50    # Top K features to select
PCA_COMPONENTS = 10             # Number of PCA components
RANDOM_STATE = 42               # Random state for reproducibility
# --------------------------------------

print("="*60)
print("CPTAC-GBM MULTI-OMICS INTEGRATION PIPELINE")
print("="*60)

# --------- 1. LOAD PROCESSED DATA ---------
print("\n1. Loading processed omics data...")

try:
    clinical_df = pd.read_csv(CLINICAL_FILE, index_col=0)
    print(f"   Clinical data: {clinical_df.shape}")
except Exception as e:
    print(f"   Error loading clinical data: {e}")
    clinical_df = None

try:
    rnaseq_df = pd.read_csv(RNASEQ_FILE, index_col=0)
    print(f"   RNA-seq data: {rnaseq_df.shape}")
except Exception as e:
    print(f"   Error loading RNA-seq data: {e}")
    rnaseq_df = None

try:
    mirna_df = pd.read_csv(MIRNA_FILE, index_col=0)
    print(f"   miRNA data: {mirna_df.shape}")
except Exception as e:
    print(f"   Error loading miRNA data: {e}")
    mirna_df = None

try:
    proteome_df = pd.read_csv(PROTEOME_FILE, index_col=0)
    print(f"   Proteome data: {proteome_df.shape}")
except Exception as e:
    print(f"   Error loading proteome data: {e}")
    proteome_df = None

try:
    copy_number_df = pd.read_csv(COPY_NUMBER_FILE, index_col=0)
    print(f"   Copy number data: {copy_number_df.shape}")
except Exception as e:
    print(f"   Error loading copy number data: {e}")
    copy_number_df = None

# --------- 2. FIND COMMON SAMPLES ---------
print("\n2. Finding common samples across omics layers...")

# Get sample IDs from each dataset
sample_sets = []
dataset_names = []

if clinical_df is not None:
    sample_sets.append(set(clinical_df.index))
    dataset_names.append("Clinical")

if rnaseq_df is not None:
    sample_sets.append(set(rnaseq_df.index))
    dataset_names.append("RNA-seq")

if mirna_df is not None:
    sample_sets.append(set(mirna_df.index))
    dataset_names.append("miRNA")

if proteome_df is not None:
    sample_sets.append(set(proteome_df.index))
    dataset_names.append("Proteome")

if copy_number_df is not None:
    sample_sets.append(set(copy_number_df.index))
    dataset_names.append("Copy Number")

if not sample_sets:
    print("   ERROR: No valid datasets loaded!")
    exit(1)

# Find intersection of all sample sets
common_samples = sample_sets[0]
for sample_set in sample_sets[1:]:
    common_samples = common_samples.intersection(sample_set)

common_samples = sorted(list(common_samples))
print(f"   Common samples across all datasets: {len(common_samples)}")

for i, (dataset_name, sample_set) in enumerate(zip(dataset_names, sample_sets)):
    print(f"   {dataset_name}: {len(sample_set)} samples")

# --------- 3. SUBSET TO COMMON SAMPLES ---------
print("\n3. Subsetting datasets to common samples...")

if clinical_df is not None:
    clinical_df = clinical_df.loc[common_samples]
    print(f"   Clinical data subset: {clinical_df.shape}")

if rnaseq_df is not None:
    rnaseq_df = rnaseq_df.loc[common_samples]
    print(f"   RNA-seq data subset: {rnaseq_df.shape}")

if mirna_df is not None:
    mirna_df = mirna_df.loc[common_samples]
    print(f"   miRNA data subset: {mirna_df.shape}")

if proteome_df is not None:
    proteome_df = proteome_df.loc[common_samples]
    print(f"   Proteome data subset: {proteome_df.shape}")

if copy_number_df is not None:
    copy_number_df = copy_number_df.loc[common_samples]
    print(f"   Copy number data subset: {copy_number_df.shape}")

# --------- 4. EXTRACT RESPONSE LABELS ---------
print("\n4. Extracting response labels...")

# Get response labels (should be consistent across datasets)
response_labels = None
if clinical_df is not None and 'response' in clinical_df.columns:
    response_labels = clinical_df['response']
elif rnaseq_df is not None and 'response' in rnaseq_df.columns:
    response_labels = rnaseq_df['response']
elif mirna_df is not None and 'response' in mirna_df.columns:
    response_labels = mirna_df['response']
elif proteome_df is not None and 'response' in proteome_df.columns:
    response_labels = proteome_df['response']
elif copy_number_df is not None and 'response' in copy_number_df.columns:
    response_labels = copy_number_df['response']

if response_labels is not None:
    response_counts = response_labels.value_counts()
    print(f"   Response distribution: {response_counts.to_dict()}")
else:
    print("   WARNING: No response labels found!")

# --------- 5. PREPARE FEATURE MATRICES ---------
print("\n5. Preparing feature matrices...")

feature_matrices = []
feature_prefixes = []

# Clinical features
if clinical_df is not None:
    clinical_features = clinical_df.select_dtypes(include=[np.number])
    if not clinical_features.empty:
        feature_matrices.append(clinical_features)
        feature_prefixes.append("clinical")
        print(f"   Clinical features: {clinical_features.shape[1]}")

# RNA-seq features
if rnaseq_df is not None:
    rnaseq_features = rnaseq_df.select_dtypes(include=[np.number])
    
    # Apply TPM normalization if enabled
    if ENABLE_TPM_NORMALIZATION:
        print("   Applying TPM normalization to RNA-seq data...")
        # Convert log2 values back to linear scale
        linear_values = 2 ** rnaseq_features
        # Apply TPM normalization
        tpm_values = (linear_values.div(linear_values.sum(axis=1), axis=0) * TPM_SCALING_FACTOR)
        # Convert back to log2 scale (add pseudocount to avoid log(0))
        rnaseq_features = np.log2(tpm_values + 1)
    
    if not rnaseq_features.empty:
        feature_matrices.append(rnaseq_features)
        feature_prefixes.append("rnaseq")
        print(f"   RNA-seq features: {rnaseq_features.shape[1]}")

# miRNA features
if mirna_df is not None:
    mirna_features = mirna_df.select_dtypes(include=[np.number])
    if not mirna_features.empty:
        feature_matrices.append(mirna_features)
        feature_prefixes.append("mirna")
        print(f"   miRNA features: {mirna_features.shape[1]}")

# Proteome features
if proteome_df is not None:
    proteome_features = proteome_df.select_dtypes(include=[np.number])
    if not proteome_features.empty:
        feature_matrices.append(proteome_features)
        feature_prefixes.append("proteome")
        print(f"   Proteome features: {proteome_features.shape[1]}")

# Copy number features
if copy_number_df is not None:
    copy_number_features = copy_number_df.select_dtypes(include=[np.number])
    if not copy_number_features.empty:
        feature_matrices.append(copy_number_features)
        feature_prefixes.append("copy_number")
        print(f"   Copy number features: {copy_number_features.shape[1]}")

# --------- 6. COMBINE FEATURE MATRICES ---------
print("\n6. Combining feature matrices...")

if not feature_matrices:
    print("   ERROR: No feature matrices available!")
    exit(1)

# Add prefixes to column names to avoid conflicts
prefixed_matrices = []
for matrix, prefix in zip(feature_matrices, feature_prefixes):
    prefixed_matrix = matrix.copy()
    prefixed_matrix.columns = [f"{prefix}_{col}" for col in matrix.columns]
    prefixed_matrices.append(prefixed_matrix)

# Combine all feature matrices
integrated_features = pd.concat(prefixed_matrices, axis=1)
print(f"   Integrated feature matrix: {integrated_features.shape}")

# --------- 7. FEATURE SELECTION ---------
print("\n7. Performing feature selection...")

if response_labels is not None and len(response_labels.unique()) > 1:
    # Use Random Forest for feature importance
    rf = RandomForestClassifier(n_estimators=100, random_state=RANDOM_STATE)
    
    # Handle missing values
    X = integrated_features.fillna(integrated_features.mean())
    y = response_labels
    
    # Fit Random Forest
    rf.fit(X, y)
    
    # Get feature importances
    feature_importance = pd.DataFrame({
        'feature': X.columns,
        'importance': rf.feature_importances_
    }).sort_values('importance', ascending=False)
    
    # Select top K features
    top_features = feature_importance.head(FEATURE_SELECTION_TOP_K)['feature'].tolist()
    selected_features = X[top_features]
    
    print(f"   Selected top {len(top_features)} features")
    
    # Save feature importance
    feature_importance.to_csv(FEATURES_FILE, index=False)
    print(f"   Feature importance saved to: {FEATURES_FILE}")
    
else:
    print("   Skipping feature selection (no valid response labels)")
    selected_features = integrated_features.fillna(integrated_features.mean())

# --------- 8. DIMENSIONALITY REDUCTION ---------
print("\n8. Performing dimensionality reduction...")

# Standardize features
scaler = StandardScaler()
scaled_features = scaler.fit_transform(selected_features)

# Apply PCA
pca = PCA(n_components=min(PCA_COMPONENTS, scaled_features.shape[1]))
pca_features = pca.fit_transform(scaled_features)

# Create PCA DataFrame
pca_df = pd.DataFrame(
    pca_features,
    index=selected_features.index,
    columns=[f'PC{i+1}' for i in range(pca_features.shape[1])]
)

print(f"   PCA components: {pca_df.shape[1]}")
print(f"   Explained variance ratio: {pca.explained_variance_ratio_[:5]}")

# --------- 9. CREATE FINAL INTEGRATED DATASET ---------
print("\n9. Creating final integrated dataset...")

# Combine PCA features with response labels and metadata
final_dataset = pca_df.copy()

if response_labels is not None:
    final_dataset['response'] = response_labels

# Add WSI metadata if available
if clinical_df is not None:
    if 'num_patches' in clinical_df.columns:
        final_dataset['num_patches'] = clinical_df['num_patches']
    if 'file' in clinical_df.columns:
        final_dataset['file'] = clinical_df['file']

print(f"   Final integrated dataset: {final_dataset.shape}")

# --------- 10. SAVE RESULTS ---------
print("\n10. Saving results...")

# Save integrated dataset
final_dataset.to_csv(INTEGRATED_FILE)
print(f"   Integrated dataset saved to: {INTEGRATED_FILE}")

# --------- 11. SUMMARY STATISTICS ---------
print("\n" + "="*60)
print("CPTAC-GBM MULTI-OMICS INTEGRATION SUMMARY")
print("="*60)

summary_text = []
summary_text.append(f"Total samples: {len(common_samples)}")
summary_text.append(f"Datasets integrated: {len(feature_matrices)}")

for prefix, matrix in zip(feature_prefixes, feature_matrices):
    summary_text.append(f"{prefix.capitalize()} features: {matrix.shape[1]}")

summary_text.append(f"Total features before selection: {integrated_features.shape[1]}")
summary_text.append(f"Selected features: {selected_features.shape[1]}")
summary_text.append(f"PCA components: {pca_df.shape[1]}")

if response_labels is not None:
    response_counts = response_labels.value_counts()
    summary_text.append(f"Response distribution: {response_counts.to_dict()}")

summary_text.append(f"Final dataset shape: {final_dataset.shape}")

# Print and save summary
for line in summary_text:
    print(line)

with open(RESULTS_FILE, 'w') as f:
    f.write("CPTAC-GBM Multi-Omics Integration Results\n")
    f.write("="*50 + "\n\n")
    for line in summary_text:
        f.write(line + "\n")

print(f"\nResults summary saved to: {RESULTS_FILE}")
print("="*60)
print("INTEGRATION COMPLETE!")
print("="*60)
