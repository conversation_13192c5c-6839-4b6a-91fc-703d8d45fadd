import pandas as pd
import json
import numpy as np

# --------- USER CONFIGURATION ---------
MIRNA_FILE = "data/cptac_gbm/raw_data/mirna/cptac_gbm_mirna_mature_tpm_log2.cct"
GEO_META_FILE = "geo_response_metadata.csv"         # Optional: responder labels
WSI_JSON_PATH = r"study_patient_mapping.json"      # JSON file as described
OUTPUT_FILE = "data/cptac_gbm/processed_data/cptac_gbm_mirna_processed.csv"

# Immune-related miRNAs based on literature
IMMUNE_MIRNAS = [
    # T cell regulation and immune checkpoints
    "hsa-mir-155",      # T cell activation, immune response
    "hsa-mir-146a",     # Immune tolerance, T-reg function
    "hsa-mir-21",       # Immune suppression, M2 macrophages
    "hsa-mir-150",      # T cell development, NK cell function
    "hsa-mir-125b-1",   # T cell differentiation
    "hsa-mir-125b-2",   # T cell differentiation
    
    # Immune checkpoint regulation
    "hsa-mir-200c",     # PD-L1 regulation
    "hsa-mir-200a",     # PD-L1 regulation
    "hsa-mir-200b",     # PD-L1 regulation
    "hsa-mir-34a",      # PD-L1 regulation
    "hsa-mir-138",      # CTLA-4 regulation
    
    # Cytokine and immune signaling
    "hsa-mir-23a",      # IL-17 pathway
    "hsa-mir-23b",      # IL-17 pathway
    "hsa-mir-27a",      # Immune cell migration
    "hsa-mir-27b",      # Immune cell migration
    "hsa-mir-29a",      # T cell differentiation
    "hsa-mir-29b",      # T cell differentiation
    "hsa-mir-29c",      # T cell differentiation
    
    # Macrophage polarization
    "hsa-mir-124",      # M2 macrophage polarization
    "hsa-mir-223",      # Myeloid cell differentiation
    "hsa-mir-142",      # Hematopoietic differentiation
    
    # NK cell function
    "hsa-mir-15a",      # NK cell cytotoxicity
    "hsa-mir-16",       # NK cell cytotoxicity
    "hsa-mir-181a",     # T cell sensitivity
    "hsa-mir-181b",     # T cell sensitivity
]
# --------------------------------------

# --------- 1. LOAD MIRNA DATA ---------
print("Loading CPTAC-GBM miRNA data...")
mirna_df = pd.read_csv(MIRNA_FILE, sep="\t", index_col=0)

print(f"Loaded miRNA data: {mirna_df.shape}")
print(f"Sample IDs (first 5): {list(mirna_df.columns[:5])}")

# Transpose so rows = samples, columns = miRNAs
mirna_df = mirna_df.T
mirna_df.index.name = "case_id"

print(f"Transposed data: {mirna_df.shape}")

# --------- 2. HANDLE MISSING VALUES ---------
print("Processing missing values...")
print(f"Missing values before processing: {mirna_df.isna().sum().sum()}")

# Replace NA with 0 (assuming NA means not detected/very low expression)
mirna_df = mirna_df.fillna(0)

print(f"Missing values after processing: {mirna_df.isna().sum().sum()}")

# --------- 3. FILTER FOR IMMUNE-RELATED MIRNAS ---------
print("Filtering immune-related miRNAs...")

# Find immune miRNAs that exist in the dataset
available_mirnas = []
for mirna in IMMUNE_MIRNAS:
    # Try exact match first
    if mirna in mirna_df.columns:
        available_mirnas.append(mirna)
    else:
        # Try partial matches (sometimes naming conventions differ)
        partial_matches = [col for col in mirna_df.columns if mirna.replace('hsa-mir-', '').replace('-', '') in col.replace('-', '')]
        if partial_matches:
            available_mirnas.extend(partial_matches[:1])  # Take first match

# Remove duplicates
available_mirnas = list(set(available_mirnas))
missing_mirnas = [mirna for mirna in IMMUNE_MIRNAS if mirna not in available_mirnas]

print(f"Available immune miRNAs: {len(available_mirnas)}")
print(f"Missing immune miRNAs: {len(missing_mirnas)}")

if available_mirnas:
    immune_mirna_df = mirna_df[available_mirnas].copy()
else:
    print("WARNING: No immune miRNAs found. Using top variable miRNAs instead.")
    
    # Calculate variance for each miRNA and select top variable ones
    mirna_var = mirna_df.var(axis=0).sort_values(ascending=False)
    
    # Remove miRNAs with very low expression (mean < 1)
    expressed_mirnas = mirna_df.columns[mirna_df.mean(axis=0) >= 1]
    
    if len(expressed_mirnas) > 0:
        # Select top 30 most variable expressed miRNAs
        top_mirnas = mirna_var[mirna_var.index.isin(expressed_mirnas)].head(30).index.tolist()
        immune_mirna_df = mirna_df[top_mirnas].copy()
        print(f"Selected {len(top_mirnas)} top variable miRNAs")
    else:
        # Fallback: use top 20 most variable miRNAs regardless of expression level
        top_mirnas = mirna_var.head(20).index.tolist()
        immune_mirna_df = mirna_df[top_mirnas].copy()
        print(f"Fallback: Using top 20 most variable miRNAs")

print(f"Final miRNA set: {immune_mirna_df.shape}")

# --------- 4. OPTIONAL: ADD RESPONSE LABELS ---------
print("Adding response labels...")
immune_mirna_df['response'] = 'Unknown'  # Default response

try:
    geo_df = pd.read_csv(GEO_META_FILE, index_col=0)
    print(f"Loaded GEO metadata: {geo_df.shape}")
    
    # Map response labels
    for case_id in immune_mirna_df.index:
        if case_id in geo_df.index:
            immune_mirna_df.loc[case_id, 'response'] = geo_df.loc[case_id, 'response']
    
    response_counts = immune_mirna_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")
    
except FileNotFoundError:
    print("GEO metadata file not found. Creating pseudo-response labels.")
    
    # Create pseudo-response based on miRNA expression patterns
    numeric_cols = [col for col in immune_mirna_df.columns if col != 'response']
    
    if len(numeric_cols) > 0:
        # Calculate miRNA-based immune score
        mirna_score = immune_mirna_df[numeric_cols].mean(axis=1)
        
        # Classify based on miRNA score tertiles
        q33 = mirna_score.quantile(0.33)
        q67 = mirna_score.quantile(0.67)
        
        immune_mirna_df.loc[mirna_score <= q33, 'response'] = 'Poor'
        immune_mirna_df.loc[(mirna_score > q33) & (mirna_score < q67), 'response'] = 'Intermediate'
        immune_mirna_df.loc[mirna_score >= q67, 'response'] = 'Good'
        
        print("Created pseudo-response labels based on miRNA expression")

except Exception as e:
    print(f"Error processing response labels: {e}")

# --------- 5. ADD WSI METADATA ---------
print("Adding WSI metadata...")
immune_mirna_df['num_patches'] = 'No WSI Data'
immune_mirna_df['file'] = 'No WSI Data'

try:
    with open(WSI_JSON_PATH, 'r') as f:
        wsi_mapping = json.load(f)
    
    for case_id in immune_mirna_df.index:
        if case_id in wsi_mapping:
            immune_mirna_df.loc[case_id, 'num_patches'] = wsi_mapping[case_id].get('num_patches', 'No WSI Data')
            immune_mirna_df.loc[case_id, 'file'] = wsi_mapping[case_id].get('file', 'No WSI Data')
    
    wsi_available = (immune_mirna_df['num_patches'] != 'No WSI Data').sum()
    print(f"WSI data available for {wsi_available}/{len(immune_mirna_df)} samples")
    
except FileNotFoundError:
    print("WSI mapping file not found. Using placeholder values.")
except Exception as e:
    print(f"Error loading WSI mapping: {e}")

# --------- 6. CALCULATE MIRNA-BASED IMMUNE SCORES ---------
print("Calculating miRNA-based immune scores...")

# Get numeric columns (exclude metadata)
numeric_cols = [col for col in immune_mirna_df.columns if col not in ['response', 'num_patches', 'file']]

if numeric_cols:
    # Immune checkpoint regulation score (miR-200 family, miR-34a, miR-138)
    checkpoint_mirnas = [col for col in numeric_cols if any(x in col.lower() for x in ['200a', '200b', '200c', '34a', '138'])]
    if checkpoint_mirnas:
        immune_mirna_df['checkpoint_regulation_score'] = immune_mirna_df[checkpoint_mirnas].mean(axis=1)
    
    # T cell regulation score (miR-155, miR-146a, miR-150, miR-181)
    tcell_mirnas = [col for col in numeric_cols if any(x in col.lower() for x in ['155', '146a', '150', '181a', '181b'])]
    if tcell_mirnas:
        immune_mirna_df['tcell_regulation_score'] = immune_mirna_df[tcell_mirnas].mean(axis=1)
    
    # Immune suppression score (miR-21, miR-124)
    suppression_mirnas = [col for col in numeric_cols if any(x in col.lower() for x in ['21', '124'])]
    if suppression_mirnas:
        immune_mirna_df['immune_suppression_score'] = immune_mirna_df[suppression_mirnas].mean(axis=1)
    
    # Myeloid regulation score (miR-223, miR-142)
    myeloid_mirnas = [col for col in numeric_cols if any(x in col.lower() for x in ['223', '142'])]
    if myeloid_mirnas:
        immune_mirna_df['myeloid_regulation_score'] = immune_mirna_df[myeloid_mirnas].mean(axis=1)
    
    # Overall miRNA immune score
    immune_mirna_df['mirna_immune_score'] = immune_mirna_df[numeric_cols].mean(axis=1)
    
    print("Calculated miRNA-based immune signature scores")

# --------- 7. SAVE PROCESSED DATA ---------
print("Saving processed miRNA data...")
immune_mirna_df.to_csv(OUTPUT_FILE)

print(f"SUCCESS: Processed miRNA data saved to: {OUTPUT_FILE}")
print(f"Final dataset shape: {immune_mirna_df.shape}")

# --------- 8. SUMMARY STATISTICS ---------
print("\n" + "="*50)
print("CPTAC-GBM MIRNA DATA SUMMARY")
print("="*50)

print(f"Total samples: {len(immune_mirna_df)}")
print(f"Total miRNAs/features: {len([col for col in immune_mirna_df.columns if col not in ['response', 'num_patches', 'file']])}")

if 'response' in immune_mirna_df.columns:
    response_counts = immune_mirna_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")

# Show expression statistics for key immune miRNAs
key_mirnas = ['hsa-mir-155', 'hsa-mir-21', 'hsa-mir-200a', 'hsa-mir-200b', 'hsa-mir-200c']
available_key_mirnas = [mirna for mirna in key_mirnas if mirna in immune_mirna_df.columns]

if available_key_mirnas:
    print("\nKey immune miRNA expression (log2 TPM):")
    for mirna in available_key_mirnas:
        mean_expr = immune_mirna_df[mirna].mean()
        std_expr = immune_mirna_df[mirna].std()
        print(f"  {mirna}: {mean_expr:.2f} ± {std_expr:.2f}")

# Show miRNA-based immune scores if calculated
score_cols = [col for col in immune_mirna_df.columns if 'score' in col.lower()]
if score_cols:
    print("\nmiRNA-based immune signature scores:")
    for score in score_cols:
        mean_score = immune_mirna_df[score].mean()
        std_score = immune_mirna_df[score].std()
        print(f"  {score}: {mean_score:.2f} ± {std_score:.2f}")

wsi_available = (immune_mirna_df['num_patches'] != 'No WSI Data').sum()
print(f"\nWSI data availability: {wsi_available}/{len(immune_mirna_df)} samples ({wsi_available/len(immune_mirna_df)*100:.1f}%)")

print("="*50)
