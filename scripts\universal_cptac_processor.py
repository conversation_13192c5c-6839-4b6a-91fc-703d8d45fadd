#!/usr/bin/env python3
"""
Universal CPTAC Multi-Omics Data Processor

This script processes multi-omics data from different CPTAC cancer datasets
with configurable parameters to handle varying file formats and structures.

Author: AI Assistant
Date: 2025-01-02
"""

import os
import pandas as pd
import numpy as np
import json
import gzip
from pathlib import Path
import argparse
import logging
from typing import Dict, List, Optional, Tuple

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class UniversalCPTACProcessor:
    """Universal processor for CPTAC multi-omics datasets"""
    
    def __init__(self, dataset_id: str, base_data_dir: str = "data"):
        self.dataset_id = dataset_id
        self.base_data_dir = Path(base_data_dir)
        self.dataset_dir = self.base_data_dir / f"cptac_{dataset_id.split('-')[1].lower()}"
        self.raw_data_dir = self.dataset_dir / "raw_data"
        self.processed_data_dir = self.dataset_dir / "processed_data"
        
        # Create processed data directory if it doesn't exist
        self.processed_data_dir.mkdir(parents=True, exist_ok=True)
        
        # Immune markers for analysis
        self.immune_markers = [
            "CD274", "CTLA4", "PDCD1", "LAG3", "TIGIT", "CD8A", "FOXP3", "GZMB",
            "CD3E", "CD4", "CD8B", "IFNG", "TNF", "IL2", "IL10", "TBX21", "RORC",
            "STAT1", "STAT3", "STAT4", "PRF1", "GNLY", "NKG7", "KLRK1", "NCAM1"
        ]
        
        # Dataset-specific configurations
        self.dataset_configs = self._load_dataset_configs()
        
    def _load_dataset_configs(self) -> Dict:
        """Load dataset-specific configurations"""
        return {
            'CPTAC-GBM': {
                'clinical_file': 'HS_CPTAC_GBM_CLI.tsi',
                'rnaseq_file': 'HS_CPTAC_GBM_rnaseq_fpkm_uq_log2.cct',
                'mirna_file': 'HS_CPTAC_GBM_mirna_mature_tpm_log2.cct',
                'proteome_file': 'HS_CPTAC_GBM_proteome_mssm_per_gene.cct',
                'phosphoproteome_file': 'HS_CPTAC_GBM_phosphoproteome_mssm_site.cct',
                'acetylome_file': 'HS_CPTAC_GBM_acetylome_mssm_site.cct',
                'copy_number_file': 'HS_CPTAC_GBM_wgs_somatic_cnv_per_gene.cct',
                'mutation_file': 'somaticwrapper_all_cases_filtered.v4.0.20200430.maf.gz',
                'circular_rna_file': 'HS_CPTAC_GBM_circular_rna_rsem_uq_log2.cct'
            },
            'CPTAC-UCEC': {
                'clinical_file': 'HS_CPTAC_UCEC_CLI.txt',
                'rnaseq_tumor_file': 'HS_CPTAC_UCEC_RNAseq_RSEM_UQ_log2_Tumor.cct',
                'rnaseq_normal_file': 'HS_CPTAC_UCEC_RNAseq_RSEM_UQ_log2_Normal.cct',
                'mirna_tumor_file': 'HS_CPTAC_UCEC_microRNA_log2_Tumor.cct',
                'mirna_normal_file': 'HS_CPTAC_UCEC_microRNA_log2_Normal.cct',
                'proteome_tumor_file': 'HS_CPTAC_UCEC_Proteomics_TMT_gene_level_Tumor.cct',
                'proteome_normal_file': 'HS_CPTAC_UCEC_Proteomics_TMT_gene_level_Normal.cct',
                'phosphoproteome_tumor_file': 'HS_CPTAC_UCEC_Phosphoproteomics_site_level_log2_Tumor.cct',
                'phosphoproteome_normal_file': 'HS_CPTAC_UCEC_Phosphoproteomics_site_level_log2_Normal.cct',
                'acetylome_tumor_file': 'HS_CPTAC_UCEC_Acetylproteomics_site_level_log2_Tumor.cct',
                'acetylome_normal_file': 'HS_CPTAC_UCEC_Acetylproteomics_site_level_log2_Normal.cct',
                'copy_number_file': 'HS_CPTAC_UCEC_SCNA_log2_gene_level.cct',
                'mutation_gene_file': 'HS_CPTAC_UCEC_SomaticMutations_gene_level.cbt',
                'mutation_site_file': 'HS_CPTAC_UCEC_SomaticMutations_site_level.cbt',
                'mutation_maf_file': 'UCEC_somatic_mutation_site_level_V2.1.maf',
                'circular_rna_tumor_file': 'HS_CPTAC_UCEC_circRNAseq_RSEM_UQ_log2_Tumor.cct',
                'circular_rna_normal_file': 'HS_CPTAC_UCEC_circRNAseq_RSEM_UQ_log2_Normal.cct',
                'methylation_file': 'HS_CPTAC_UCEC_Methylation_betaValue_gene_level_mean.cct'
            },
            'CPTAC-LUAD': {
                'clinical_file': 'HS_CPTAC_LUAD_cli.tsi',
                'rnaseq_tumor_file': 'HS_CPTAC_LUAD_rnaseq_uq_rpkm_log2_NArm_TUMOR.cct',
                'rnaseq_normal_file': 'HS_CPTAC_LUAD_rnaseq_uq_rpkm_log2_NArm_NORMAL.cct',
                'proteome_tumor_file': 'HS_CPTAC_LUAD_proteome_ratio_NArm_TUMOR.cct',
                'proteome_normal_file': 'HS_CPTAC_LUAD_proteome_ratio_NArm_NORMAL.cct',
                'phosphoproteome_tumor_file': 'HS_CPTAC_LUAD_phosphoproteome_ratio_norm_NArm_TUMOR.cct',
                'phosphoproteome_normal_file': 'HS_CPTAC_LUAD_phosphoproteome_ratio_norm_NArm_NORMAL.cct',
                'acetylome_tumor_file': 'HS_CPTAC_LUAD_acetylproteome_ratio_norm_NArm_TUMOR.cct',
                'acetylome_normal_file': 'HS_CPTAC_LUAD_acetylproteome_ratio_norm_NArm_NORMAL.cct',
                'methylation_tumor_file': 'HS_CPTAC_LUAD_methylation_mean_gene_tumor.cct',
                'methylation_normal_file': 'HS_CPTAC_LUAD_methylation_mean_gene_normal.cct',
                'copy_number_lr_file': 'HS_CPTAC_LUAD_cnv_gene_LR.cct',
                'copy_number_mzd_file': 'HS_CPTAC_LUAD_cnv_gene_MZD.cct',
                'mutation_gene_file': 'HS_CPTAC_LUAD_somatic_mutation_gene.cbt'
            },
            'CPTAC-COAD': {
                'clinical_file': 'Human__CPTAC_COAD__MS__Clinical__Clinical__03_01_2017__CPTAC__Clinical__BCM.tsi',
                'rnaseq_file': 'Human__CPTAC_COAD__UNC__RNAseq__HiSeq_RNA__03_01_2017__BCM__Gene__BCM_RSEM_UpperQuartile_log2.cct.gz',
                'mirna_file': 'Human__CPTAC_COAD__UNC__miRNAseq__GA_miR__03_01_2017__BCM__Gene__BCM_log2.cct',
                'proteome_tumor_file': 'Human__CPTAC_COAD__PNNL__Proteome__TMT__03_01_2017__BCM__Gene__PNNL_Tumor_TMT_UnsharedLogRatio.cct',
                'proteome_normal_file': 'Human__CPTAC_COAD__PNNL__Proteome__TMT__03_01_2017__BCM__Gene__PNNL_Normal_TMT_UnsharedLogRatio.cct',
                'proteome_tn_ratio_file': 'Human__CPTAC_COAD__PNNL__Proteome__TMT__03_01_2017__BCM__Gene__Tumor_Normal_log2FC.cct',
                'proteome_vu_file': 'Human__CPTAC_COAD__VU__Proteome__QExact__03_01_2017__BCM__Gene__VU_Tumor_LF_UnsharedCounts.cct',
                'phosphoproteome_tn_file': 'Human__CPTAC_COAD__PNNL__Phosphoproteome__TMT__03_01_2017__BCM__Site__Tumor_Normal_log2FC.cct.gz',
                'phosphoproteome_tumor_file': 'Human__CPTAC_COAD__PNNL__Phosphoproteome__TMT__03_01_2017__BCM__Site__Tumor_PNNL_TMT_LogRatio.cct.gz',
                'phosphoproteome_normal_file': 'Human__CPTAC_COAD__PNNL__Phosphoproteome__TMT__03_01_2017__BCM__Site__Normal_PNNL_TMT_LogRatio.cct.gz',
                'copy_number_thresh_file': 'Human__CPTAC_COAD__VU__SCNA__ExomeSeq__01_28_2016__BCM__Gene__BCM_CopyWriteR_GISTIC2_threshold.cct.gz',
                'copy_number_log_file': 'Human__CPTAC_COAD__VU__SCNA__ExomeSeq__01_28_2016__BCM__Gene__BCM_CopyWriteR_GISTIC2.cct.gz',
                'mutation_gene_file': 'Human__CPTAC_COAD__WUSM__Mutation__GAIIx__03_01_2017__BCM__Gene__GATK_Pipeline.cbt',
                'mutation_site_file': 'Human__CPTAC_COAD__WUSM__Mutation__GAIIx__03_01_2017__BCM__Site__GATK_Pipeline.cbt',
                'mutation_maf_file': 'final_oncotator_all.maf'
            },
            'CPTAC-BRCA': {
                'clinical_file': 'HS_CPTAC_BRCA_2018_CLI.tsi',
                'rnaseq_file': 'HS_CPTAC_BRCA_2018_RNA_GENE.cct',
                'proteome_file': 'HS_CPTAC_BRCA_2018_Proteome_Ratio_Norm_gene_Median.cct',
                'phosphoproteome_gene_file': 'HS_CPTAC_BRCA_2018_Phosphoproteome_Ratio_Norm_Gene_median.cct',
                'phosphoproteome_site_file': 'HS_CPTAC_BRCA_2018_Phosphoproteome_Ratio_Norm_Site.cct',
                'acetylome_gene_file': 'HS_CPTAC_BRCA_2018_Acetylome_Ratio_Norm_Gene_Median.cct',
                'acetylome_site_file': 'HS_CPTAC_BRCA_2018_Acetylome_Ratio_Norm_Site.cct',
                'copy_number_file': 'HS_CPTAC_BRCA_2018_CNA.cct',
                'mutation_gene_file': 'HS_CPTAC_BRCA_2018_MUT_GENE.cbt'
            },
            'CPTAC-LSCC': {
                'clinical_tumor_file': 'HS_CPTAC_LSCC_2020_clinical_phenotypes_TUMOR.tsi',
                'clinical_normal_file': 'HS_CPTAC_LSCC_2020_clinical_phenotypes_NORMAL.tsi',
                'molecular_tumor_file': 'HS_CPTAC_LSCC_2020_molecular_phenotypes_TUMOR.tsi',
                'molecular_normal_file': 'HS_CPTAC_LSCC_2020_molecular_phenotypes_NORMAL.tsi',
                'rnaseq_tumor_file': 'HS_CPTAC_LSCC_2020_rnaseq_uq_fpkm_log2_NArm_TUMOR.cct',
                'rnaseq_normal_file': 'HS_CPTAC_LSCC_2020_rnaseq_uq_fpkm_log2_NArm_NORMAL.cct',
                'proteome_tumor_file': 'HS_CPTAC_LSCC_2020_proteome_ratio_NArm_TUMOR.cct',
                'proteome_normal_file': 'HS_CPTAC_LSCC_2020_proteome_ratio_NArm_NORMAL.cct',
                'phosphoproteome_tumor_file': 'HS_CPTAC_LSCC_2020_phospho_ratio_norm_NArm_TUMOR.cct',
                'phosphoproteome_normal_file': 'HS_CPTAC_LSCC_2020_phospho_ratio_norm_NArm_NORMAL.cct',
                'acetylome_tumor_file': 'HS_CPTAC_LSCC_2020_acetyl_ratio_norm_NArm_TUMOR.cct',
                'acetylome_normal_file': 'HS_CPTAC_LSCC_2020_acetyl_ratio_norm_NArm_NORMAL.cct',
                'ubiquitin_tumor_file': 'HS_CPTAC_LSCC_2020_ubiquitin_ratio_norm_NArm_TUMOR.cct',
                'ubiquitin_normal_file': 'HS_CPTAC_LSCC_2020_ubiquitin_ratio_norm_NArm_NORMAL.cct',
                'methylation_tumor_file': 'HS_CPTAC_LSCC_2020_methylation_mean_gene_tumor.cct',
                'methylation_normal_file': 'HS_CPTAC_LSCC_2020_methylation_mean_gene_normal.cct',
                'copy_number_file': 'HS_CPTAC_LSCC_2020_cnv_gene_gistic2_log_ratio.cct',
                'mirna_tumor_file': 'HS_CPTAC_LSCC_2020_miRNA_tpm_log2_NArm_TUMOR.cct',
                'mirna_normal_file': 'HS_CPTAC_LSCC_2020_miRNA_tpm_log2_NArm_NORMAL.cct',
                'circular_rna_tumor_file': 'HS_CPTAC_LSCC_2020_ciRNA_rsem_uq_log2_NArm_TUMOR.cct',
                'circular_rna_normal_file': 'HS_CPTAC_LSCC_2020_ciRNA_rsem_uq_log2_NArm_NORMAL.cct',
                'mutation_gene_file': 'HS_CPTAC_LSCC_2020_somatic_mutation_gene.cbt'
            }
        }
    
    def _load_data_file(self, filename: str, sep: str = '\t') -> Optional[pd.DataFrame]:
        """Load a data file with automatic compression detection and encoding handling"""
        file_path = self.raw_data_dir / filename

        if not file_path.exists():
            logger.warning(f"File not found: {file_path}")
            return None

        # Try different encodings
        encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                # Handle compressed files
                if filename.endswith('.gz'):
                    with gzip.open(file_path, 'rt', encoding=encoding) as f:
                        df = pd.read_csv(f, sep=sep, index_col=0)
                else:
                    df = pd.read_csv(file_path, sep=sep, index_col=0, encoding=encoding)

                logger.info(f"Loaded {filename} with {encoding} encoding: {df.shape}")
                return df

            except UnicodeDecodeError:
                continue
            except Exception as e:
                logger.error(f"Error loading {filename} with {encoding}: {e}")
                continue

        logger.error(f"Failed to load {filename} with any encoding")
        return None
    
    def process_clinical_data(self) -> Optional[pd.DataFrame]:
        """Process clinical data"""
        logger.info("Processing clinical data...")

        config = self.dataset_configs.get(self.dataset_id, {})

        # Handle different clinical file naming patterns
        clinical_file = None
        if 'clinical_file' in config:
            clinical_file = config['clinical_file']
        elif 'clinical_tumor_file' in config:
            clinical_file = config['clinical_tumor_file']

        if not clinical_file:
            logger.warning(f"No clinical file configured for {self.dataset_id}")
            return None

        # Try different separators for clinical files
        for sep in ['\t', ',']:
            clinical_df = self._load_data_file(clinical_file, sep=sep)
            if clinical_df is not None:
                break

        if clinical_df is None:
            return None

        # Save processed clinical data
        output_file = self.processed_data_dir / f"{self.dataset_id.lower()}_clinical_processed.csv"
        clinical_df.to_csv(output_file)
        logger.info(f"Saved processed clinical data: {output_file}")

        return clinical_df
    
    def process_rnaseq_data(self, sample_type: str = 'tumor') -> Optional[pd.DataFrame]:
        """Process RNA-seq data"""
        logger.info(f"Processing RNA-seq data ({sample_type})...")
        
        config = self.dataset_configs.get(self.dataset_id, {})
        
        # Determine RNA-seq file based on dataset and sample type
        if sample_type == 'tumor' and f'rnaseq_{sample_type}_file' in config:
            rnaseq_file = config[f'rnaseq_{sample_type}_file']
        elif sample_type == 'normal' and f'rnaseq_{sample_type}_file' in config:
            rnaseq_file = config[f'rnaseq_{sample_type}_file']
        elif 'rnaseq_file' in config:
            rnaseq_file = config['rnaseq_file']
        else:
            logger.warning(f"No RNA-seq file configured for {self.dataset_id} ({sample_type})")
            return None
        
        rnaseq_df = self._load_data_file(rnaseq_file)
        if rnaseq_df is None:
            return None
        
        # Transpose so rows = samples, columns = genes
        rnaseq_df = rnaseq_df.T
        rnaseq_df.index.name = "case_id"
        
        # Filter for immune markers
        available_markers = [marker for marker in self.immune_markers if marker in rnaseq_df.columns]
        missing_markers = [marker for marker in self.immune_markers if marker not in rnaseq_df.columns]
        
        logger.info(f"Available immune markers: {len(available_markers)}")
        if missing_markers:
            logger.info(f"Missing immune markers: {len(missing_markers)}")
        
        # Extract immune markers or use immune-related genes
        if available_markers:
            immune_df = rnaseq_df[available_markers].copy()
        else:
            logger.info("Using immune-related gene search...")
            # Look for immune-related genes
            immune_related_keywords = ['CD', 'IL', 'TNF', 'IFNG', 'FOXP3', 'GZMB', 'PRF1', 'TBX21', 'RORC', 'STAT']
            immune_genes = []
            
            for gene in rnaseq_df.columns:
                if any(keyword in gene.upper() for keyword in immune_related_keywords):
                    immune_genes.append(gene)
            
            if immune_genes:
                immune_df = rnaseq_df[immune_genes[:50]].copy()  # Limit to top 50
                logger.info(f"Found {len(immune_genes)} immune-related genes")
            else:
                # Use top variable genes as fallback
                gene_var = rnaseq_df.var(axis=0).sort_values(ascending=False)
                immune_df = rnaseq_df[gene_var.index[:50]].copy()
                logger.info("Using top 50 variable genes as fallback")
        
        # Save processed RNA-seq data
        suffix = f"_{sample_type}" if sample_type != 'tumor' else ""
        output_file = self.processed_data_dir / f"{self.dataset_id.lower()}_rnaseq{suffix}_processed.csv"
        immune_df.to_csv(output_file)
        logger.info(f"Saved processed RNA-seq data: {output_file}")
        
        return immune_df

    def process_proteome_data(self, sample_type: str = 'tumor') -> Optional[pd.DataFrame]:
        """Process proteome data"""
        logger.info(f"Processing proteome data ({sample_type})...")
        
        config = self.dataset_configs.get(self.dataset_id, {})
        
        # Determine proteome file based on dataset and sample type
        if sample_type == 'tumor' and f'proteome_{sample_type}_file' in config:
            proteome_file = config[f'proteome_{sample_type}_file']
        elif sample_type == 'normal' and f'proteome_{sample_type}_file' in config:
            proteome_file = config[f'proteome_{sample_type}_file']
        elif 'proteome_file' in config:
            proteome_file = config['proteome_file']
        else:
            logger.warning(f"No proteome file configured for {self.dataset_id} ({sample_type})")
            return None
        
        proteome_df = self._load_data_file(proteome_file)
        if proteome_df is None:
            return None
        
        # Transpose so rows = samples, columns = proteins
        proteome_df = proteome_df.T
        proteome_df.index.name = "case_id"
        
        # Filter for immune-related proteins
        immune_proteins = []
        for protein in proteome_df.columns:
            if any(marker in protein.upper() for marker in self.immune_markers):
                immune_proteins.append(protein)
        
        if immune_proteins:
            immune_proteome_df = proteome_df[immune_proteins].copy()
            logger.info(f"Found {len(immune_proteins)} immune-related proteins")
        else:
            # Use top variable proteins as fallback
            protein_var = proteome_df.var(axis=0).sort_values(ascending=False)
            immune_proteome_df = proteome_df[protein_var.index[:50]].copy()
            logger.info("Using top 50 variable proteins as fallback")
        
        # Save processed proteome data
        suffix = f"_{sample_type}" if sample_type != 'tumor' else ""
        output_file = self.processed_data_dir / f"{self.dataset_id.lower()}_proteome{suffix}_processed.csv"
        immune_proteome_df.to_csv(output_file)
        logger.info(f"Saved processed proteome data: {output_file}")
        
        return immune_proteome_df

def main():
    """Main processing function"""
    parser = argparse.ArgumentParser(description='Universal CPTAC Multi-Omics Processor')
    parser.add_argument('dataset_id', help='CPTAC dataset ID (e.g., CPTAC-GBM, CPTAC-UCEC)')
    parser.add_argument('--data-types', nargs='+', default=['clinical', 'rnaseq', 'proteome'],
                       help='Data types to process')
    parser.add_argument('--sample-types', nargs='+', default=['tumor'],
                       help='Sample types to process (tumor, normal)')
    parser.add_argument('--base-dir', default='data', help='Base data directory')
    
    args = parser.parse_args()
    
    # Initialize processor
    processor = UniversalCPTACProcessor(args.dataset_id, args.base_dir)
    
    logger.info(f"Processing {args.dataset_id} dataset...")
    logger.info(f"Data types: {args.data_types}")
    logger.info(f"Sample types: {args.sample_types}")
    
    results = {}
    
    # Process each data type
    if 'clinical' in args.data_types:
        results['clinical'] = processor.process_clinical_data()
    
    if 'rnaseq' in args.data_types:
        for sample_type in args.sample_types:
            results[f'rnaseq_{sample_type}'] = processor.process_rnaseq_data(sample_type)
    
    if 'proteome' in args.data_types:
        for sample_type in args.sample_types:
            results[f'proteome_{sample_type}'] = processor.process_proteome_data(sample_type)
    
    # Summary
    logger.info("Processing complete!")
    for data_type, result in results.items():
        if result is not None:
            logger.info(f"  {data_type}: {result.shape}")
        else:
            logger.info(f"  {data_type}: Failed")

if __name__ == "__main__":
    main()
