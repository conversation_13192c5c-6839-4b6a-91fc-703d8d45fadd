import pandas as pd
import json

# --------- USER CONFIGURATION ---------
PROTEOME_FILE = "data/cptac_gbm/raw_data/proteome/cptac_gbm_proteome_mssm_per_gene.cct"
GEO_META_FILE = "geo_response_metadata.csv"         # Optional: responder labels
WSI_JSON_PATH = r"study_patient_mapping.json"      # JSON file as described
OUTPUT_FILE = "data/cptac_gbm/processed_data/cptac_gbm_proteome_processed.csv"

# Immune-related proteins for proteome analysis
IMMUNE_PROTEINS = [
    # Immune checkpoint proteins
    "CD274",    # PD-L1
    "PDCD1",    # PD-1
    "CTLA4",    # CTLA-4
    "LAG3",     # LAG-3
    "TIGIT",    # TIGIT
    "HAVCR2",   # TIM-3
    "BTLA",     # BTLA
    "ICOS",     # ICOS
    "ICOSLG",   # ICOS-L
    
    # T cell markers and function
    "CD8A",     # CD8+ T cells
    "CD8B",     # CD8+ T cells
    "CD4",      # CD4+ T cells
    "FOXP3",    # Regulatory T cells
    "GZMB",     # Granzyme B
    "PRF1",     # Perforin 1
    "TBX21",    # T-bet (Th1)
    "RORC",     # RORγt (Th17)
    "STAT1",    # STAT1 (IFN signaling)
    "STAT3",    # STAT3 (IL-6/IL-10 signaling)
    "STAT4",    # STAT4 (IL-12 signaling)
    "STAT6",    # STAT6 (IL-4 signaling)
    
    # Cytokines and chemokines
    "IFNG",     # IFN-γ
    "TNF",      # TNF-α
    "IL2",      # IL-2
    "IL6",      # IL-6
    "IL10",     # IL-10
    "IL12A",    # IL-12A
    "IL12B",    # IL-12B
    "IL17A",    # IL-17A
    "IL4",      # IL-4
    "CXCL9",    # CXCL9
    "CXCL10",   # CXCL10
    "CCL2",     # CCL2/MCP-1
    "CCL5",     # CCL5/RANTES
    
    # Antigen presentation
    "HLA-A",    # MHC Class I
    "HLA-B",    # MHC Class I
    "HLA-C",    # MHC Class I
    "HLA-DRA",  # MHC Class II
    "HLA-DRB1", # MHC Class II
    "B2M",      # Beta-2 microglobulin
    "TAP1",     # Transporter 1
    "TAP2",     # Transporter 2
    
    # Complement system
    "C1QA",     # Complement C1q A
    "C1QB",     # Complement C1q B
    "C1QC",     # Complement C1q C
    "C3",       # Complement C3
    "C5",       # Complement C5
    
    # Immune cell markers
    "CD68",     # Macrophages
    "CD163",    # M2 macrophages
    "CD86",     # Dendritic cells
    "NCAM1",    # NK cells (CD56)
    "KLRK1",    # NKG2D
]
# --------------------------------------

# --------- 1. LOAD PROTEOME DATA ---------
print("Loading CPTAC-GBM proteome data...")
proteome_df = pd.read_csv(PROTEOME_FILE, sep="\t", index_col=0)

print(f"Loaded proteome data: {proteome_df.shape}")
print(f"Sample IDs (first 5): {list(proteome_df.columns[:5])}")

# Transpose so rows = samples, columns = proteins
proteome_df = proteome_df.T
proteome_df.index.name = "case_id"

print(f"Transposed data: {proteome_df.shape}")

# --------- 2. FILTER FOR IMMUNE PROTEINS ---------
print("Filtering immune-related proteins...")

# Find immune proteins that exist in the dataset
available_proteins = [protein for protein in IMMUNE_PROTEINS if protein in proteome_df.columns]
missing_proteins = [protein for protein in IMMUNE_PROTEINS if protein not in proteome_df.columns]

print(f"Available immune proteins: {len(available_proteins)}")
print(f"Missing immune proteins: {len(missing_proteins)}")

if available_proteins:
    immune_proteome_df = proteome_df[available_proteins].copy()
    print(f"Available proteins: {available_proteins}")
else:
    print("WARNING: No immune proteins found. Using top variable proteins instead.")
    
    # Calculate variance for each protein and select top variable ones
    protein_var = proteome_df.var(axis=0).sort_values(ascending=False)
    
    # Look for immune-related proteins in top variable proteins
    immune_keywords = ['CD', 'IL', 'TNF', 'IFNG', 'HLA', 'STAT', 'CXCL', 'CCL', 'GZMB', 'PRF1', 'FOXP3']
    immune_proteins = []
    
    for protein in protein_var.index[:1000]:  # Check top 1000 variable proteins
        if any(keyword in protein.upper() for keyword in immune_keywords):
            immune_proteins.append(protein)
        if len(immune_proteins) >= 50:  # Limit to top 50 immune-related proteins
            break
    
    if immune_proteins:
        immune_proteome_df = proteome_df[immune_proteins].copy()
        print(f"Selected {len(immune_proteins)} immune-related proteins from variable proteins")
    else:
        # Fallback: use top 30 most variable proteins
        top_proteins = protein_var.head(30).index.tolist()
        immune_proteome_df = proteome_df[top_proteins].copy()
        print(f"Fallback: Using top 30 most variable proteins")

print(f"Final immune protein set: {immune_proteome_df.shape}")

# --------- 3. OPTIONAL: ADD RESPONSE LABELS ---------
print("Adding response labels...")
immune_proteome_df['response'] = 'Unknown'  # Default response

try:
    geo_df = pd.read_csv(GEO_META_FILE, index_col=0)
    print(f"Loaded GEO metadata: {geo_df.shape}")
    
    # Map response labels
    for case_id in immune_proteome_df.index:
        if case_id in geo_df.index:
            immune_proteome_df.loc[case_id, 'response'] = geo_df.loc[case_id, 'response']
    
    response_counts = immune_proteome_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")
    
except FileNotFoundError:
    print("GEO metadata file not found. Creating pseudo-response labels.")
    
    # Create pseudo-response based on protein expression patterns
    numeric_cols = [col for col in immune_proteome_df.columns if col != 'response']
    
    if len(numeric_cols) > 0:
        # Calculate protein-based immune score
        protein_score = immune_proteome_df[numeric_cols].mean(axis=1)
        
        # Classify based on protein score tertiles
        q33 = protein_score.quantile(0.33)
        q67 = protein_score.quantile(0.67)
        
        immune_proteome_df.loc[protein_score <= q33, 'response'] = 'Poor'
        immune_proteome_df.loc[(protein_score > q33) & (protein_score < q67), 'response'] = 'Intermediate'
        immune_proteome_df.loc[protein_score >= q67, 'response'] = 'Good'
        
        print("Created pseudo-response labels based on protein expression")

except Exception as e:
    print(f"Error processing response labels: {e}")

# --------- 4. ADD WSI METADATA ---------
print("Adding WSI metadata...")
immune_proteome_df['num_patches'] = 'No WSI Data'
immune_proteome_df['file'] = 'No WSI Data'

try:
    with open(WSI_JSON_PATH, 'r') as f:
        wsi_mapping = json.load(f)
    
    for case_id in immune_proteome_df.index:
        if case_id in wsi_mapping:
            immune_proteome_df.loc[case_id, 'num_patches'] = wsi_mapping[case_id].get('num_patches', 'No WSI Data')
            immune_proteome_df.loc[case_id, 'file'] = wsi_mapping[case_id].get('file', 'No WSI Data')
    
    wsi_available = (immune_proteome_df['num_patches'] != 'No WSI Data').sum()
    print(f"WSI data available for {wsi_available}/{len(immune_proteome_df)} samples")
    
except FileNotFoundError:
    print("WSI mapping file not found. Using placeholder values.")
except Exception as e:
    print(f"Error loading WSI mapping: {e}")

# --------- 5. CALCULATE PROTEIN-BASED IMMUNE SCORES ---------
print("Calculating protein-based immune scores...")

# Get numeric columns (exclude metadata)
numeric_cols = [col for col in immune_proteome_df.columns if col not in ['response', 'num_patches', 'file']]

if numeric_cols:
    # Immune checkpoint score
    checkpoint_proteins = [col for col in numeric_cols if col in ['CD274', 'PDCD1', 'CTLA4', 'LAG3', 'TIGIT', 'HAVCR2']]
    if checkpoint_proteins:
        immune_proteome_df['checkpoint_protein_score'] = immune_proteome_df[checkpoint_proteins].mean(axis=1)
    
    # T cell cytotoxicity score
    cytotox_proteins = [col for col in numeric_cols if col in ['CD8A', 'CD8B', 'GZMB', 'PRF1']]
    if cytotox_proteins:
        immune_proteome_df['cytotoxic_protein_score'] = immune_proteome_df[cytotox_proteins].mean(axis=1)
    
    # Antigen presentation score
    mhc_proteins = [col for col in numeric_cols if col in ['HLA-A', 'HLA-B', 'HLA-C', 'HLA-DRA', 'HLA-DRB1', 'B2M', 'TAP1', 'TAP2']]
    if mhc_proteins:
        immune_proteome_df['antigen_presentation_score'] = immune_proteome_df[mhc_proteins].mean(axis=1)
    
    # Cytokine signaling score
    cytokine_proteins = [col for col in numeric_cols if col in ['IFNG', 'TNF', 'IL2', 'IL6', 'IL10', 'IL12A', 'IL12B']]
    if cytokine_proteins:
        immune_proteome_df['cytokine_signaling_score'] = immune_proteome_df[cytokine_proteins].mean(axis=1)
    
    # STAT signaling score
    stat_proteins = [col for col in numeric_cols if col in ['STAT1', 'STAT3', 'STAT4', 'STAT6']]
    if stat_proteins:
        immune_proteome_df['stat_signaling_score'] = immune_proteome_df[stat_proteins].mean(axis=1)
    
    # Overall protein immune score
    immune_proteome_df['protein_immune_score'] = immune_proteome_df[numeric_cols].mean(axis=1)
    
    print("Calculated protein-based immune signature scores")

# --------- 6. SAVE PROCESSED DATA ---------
print("Saving processed proteome data...")
immune_proteome_df.to_csv(OUTPUT_FILE)

print(f"SUCCESS: Processed proteome data saved to: {OUTPUT_FILE}")
print(f"Final dataset shape: {immune_proteome_df.shape}")

# --------- 7. SUMMARY STATISTICS ---------
print("\n" + "="*50)
print("CPTAC-GBM PROTEOME DATA SUMMARY")
print("="*50)

print(f"Total samples: {len(immune_proteome_df)}")
print(f"Total proteins/features: {len([col for col in immune_proteome_df.columns if col not in ['response', 'num_patches', 'file']])}")

if 'response' in immune_proteome_df.columns:
    response_counts = immune_proteome_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")

# Show expression statistics for key immune proteins
key_proteins = ['CD274', 'PDCD1', 'CTLA4', 'CD8A', 'FOXP3', 'GZMB']
available_key_proteins = [protein for protein in key_proteins if protein in immune_proteome_df.columns]

if available_key_proteins:
    print("\nKey immune protein expression (TMT log2 ratio):")
    for protein in available_key_proteins:
        mean_expr = immune_proteome_df[protein].mean()
        std_expr = immune_proteome_df[protein].std()
        print(f"  {protein}: {mean_expr:.2f} ± {std_expr:.2f}")

# Show protein-based immune scores if calculated
score_cols = [col for col in immune_proteome_df.columns if 'score' in col.lower()]
if score_cols:
    print("\nProtein-based immune signature scores:")
    for score in score_cols:
        mean_score = immune_proteome_df[score].mean()
        std_score = immune_proteome_df[score].std()
        print(f"  {score}: {mean_score:.2f} ± {std_score:.2f}")

wsi_available = (immune_proteome_df['num_patches'] != 'No WSI Data').sum()
print(f"\nWSI data availability: {wsi_available}/{len(immune_proteome_df)} samples ({wsi_available/len(immune_proteome_df)*100:.1f}%)")

print("="*50)
