import pandas as pd
import json

# --------- USER CONFIGURATION ---------
RNA_FILE = "data/cptac_gbm/raw_data/rnaseq/cptac_gbm_rnaseq_fpkm_uq_log2.cct"
GEO_META_FILE = "geo_response_metadata.csv"         # Optional: responder labels
WSI_JSON_PATH = r"study_patient_mapping.json"      # JSON file as described
IMMUNE_MARKERS = ["CD274", "CTLA4", "PDCD1", "LAG3", "TIGIT", "CD8A", "FOXP3", "GZMB"]
OUTPUT_FILE = "data/cptac_gbm/processed_data/cptac_gbm_rnaseq_processed.csv"
# --------------------------------------

# --------- 1. LOAD RNA-SEQ DATA ---------
print("Loading CPTAC-GBM RNA-seq data...")
rna_df = pd.read_csv(RNA_FILE, sep="\t", index_col=0)

# Data is already in log2 FPKM format, samples are columns
print(f"Loaded RNA-seq data: {rna_df.shape}")
print(f"Sample IDs (first 5): {list(rna_df.columns[:5])}")

# Transpose so rows = samples, columns = genes
rna_df = rna_df.T
rna_df.index.name = "case_id"

print(f"Transposed data: {rna_df.shape}")

# --------- 2. FILTER FOR IMMUNE MARKERS ---------
print("Filtering immune-related genes...")

# Find immune markers that exist in the dataset
available_markers = [marker for marker in IMMUNE_MARKERS if marker in rna_df.columns]
missing_markers = [marker for marker in IMMUNE_MARKERS if marker not in rna_df.columns]

print(f"Available immune markers: {available_markers}")
if missing_markers:
    print(f"Missing immune markers: {missing_markers}")

# Extract immune markers
if available_markers:
    immune_df = rna_df[available_markers].copy()
else:
    print("WARNING: No immune markers found. Using top variable genes instead.")
    # Calculate variance for each gene and select top immune-related genes
    gene_var = rna_df.var(axis=0).sort_values(ascending=False)
    
    # Look for immune-related genes in top variable genes
    immune_related_keywords = ['CD', 'IL', 'TNF', 'IFNG', 'FOXP3', 'GZMB', 'PRF1', 'TBX21', 'RORC', 'STAT']
    immune_genes = []
    
    for gene in gene_var.index[:1000]:  # Check top 1000 variable genes
        if any(keyword in gene.upper() for keyword in immune_related_keywords):
            immune_genes.append(gene)
        if len(immune_genes) >= 50:  # Limit to top 50 immune-related genes
            break
    
    if immune_genes:
        immune_df = rna_df[immune_genes].copy()
        print(f"Selected {len(immune_genes)} immune-related genes from variable genes")
    else:
        # Fallback: use top 20 most variable genes
        top_genes = gene_var.head(20).index.tolist()
        immune_df = rna_df[top_genes].copy()
        print(f"Fallback: Using top 20 most variable genes")

print(f"Final immune gene set: {immune_df.shape}")

# --------- 3. OPTIONAL: ADD RESPONSE LABELS ---------
print("Adding response labels...")
immune_df['response'] = 'Unknown'  # Default response

# Try to load GEO metadata if available
try:
    geo_df = pd.read_csv(GEO_META_FILE, index_col=0)
    print(f"Loaded GEO metadata: {geo_df.shape}")
    
    # Map response labels
    for case_id in immune_df.index:
        if case_id in geo_df.index:
            immune_df.loc[case_id, 'response'] = geo_df.loc[case_id, 'response']
    
    response_counts = immune_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")
    
except FileNotFoundError:
    print("GEO metadata file not found. Using default response labels.")
    
    # Create pseudo-response based on immune gene expression
    # High immune gene expression might indicate better response
    if len(immune_df.columns) > 1:  # Exclude 'response' column
        immune_cols = [col for col in immune_df.columns if col != 'response']
        immune_score = immune_df[immune_cols].mean(axis=1)
        
        # Classify based on immune score tertiles
        q33 = immune_score.quantile(0.33)
        q67 = immune_score.quantile(0.67)
        
        immune_df.loc[immune_score <= q33, 'response'] = 'Poor'
        immune_df.loc[(immune_score > q33) & (immune_score < q67), 'response'] = 'Intermediate'
        immune_df.loc[immune_score >= q67, 'response'] = 'Good'
        
        print("Created pseudo-response labels based on immune gene expression")

except Exception as e:
    print(f"Error processing response labels: {e}")

# --------- 4. ADD WSI METADATA ---------
print("Adding WSI metadata...")
immune_df['num_patches'] = 'No WSI Data'
immune_df['file'] = 'No WSI Data'

try:
    with open(WSI_JSON_PATH, 'r') as f:
        wsi_mapping = json.load(f)
    
    for case_id in immune_df.index:
        if case_id in wsi_mapping:
            immune_df.loc[case_id, 'num_patches'] = wsi_mapping[case_id].get('num_patches', 'No WSI Data')
            immune_df.loc[case_id, 'file'] = wsi_mapping[case_id].get('file', 'No WSI Data')
    
    wsi_available = (immune_df['num_patches'] != 'No WSI Data').sum()
    print(f"WSI data available for {wsi_available}/{len(immune_df)} samples")
    
except FileNotFoundError:
    print("WSI mapping file not found. Using placeholder values.")
except Exception as e:
    print(f"Error loading WSI mapping: {e}")

# --------- 5. CALCULATE IMMUNE SCORES ---------
print("Calculating immune scores...")

# Get numeric columns (exclude metadata)
numeric_cols = [col for col in immune_df.columns if col not in ['response', 'num_patches', 'file']]

if numeric_cols:
    # T cell cytotoxicity score (CD8A, GZMB, PRF1)
    cytotox_genes = [gene for gene in ['CD8A', 'GZMB', 'PRF1'] if gene in numeric_cols]
    if cytotox_genes:
        immune_df['cytotoxic_score'] = immune_df[cytotox_genes].mean(axis=1)
    
    # Immune checkpoint score (PD-1, PD-L1, CTLA4, LAG3, TIGIT)
    checkpoint_genes = [gene for gene in ['PDCD1', 'CD274', 'CTLA4', 'LAG3', 'TIGIT'] if gene in numeric_cols]
    if checkpoint_genes:
        immune_df['checkpoint_score'] = immune_df[checkpoint_genes].mean(axis=1)
    
    # Regulatory T cell score (FOXP3)
    if 'FOXP3' in numeric_cols:
        immune_df['treg_score'] = immune_df['FOXP3']
    
    # Overall immune activation score
    immune_df['immune_activation_score'] = immune_df[numeric_cols].mean(axis=1)
    
    print("Calculated immune signature scores")

# --------- 6. SAVE PROCESSED DATA ---------
print("Saving processed RNA-seq data...")
immune_df.to_csv(OUTPUT_FILE)

print(f"SUCCESS: Processed RNA-seq data saved to: {OUTPUT_FILE}")
print(f"Final dataset shape: {immune_df.shape}")

# --------- 7. SUMMARY STATISTICS ---------
print("\n" + "="*50)
print("CPTAC-GBM RNA-SEQ DATA SUMMARY")
print("="*50)

print(f"Total samples: {len(immune_df)}")
print(f"Total genes/features: {len([col for col in immune_df.columns if col not in ['response', 'num_patches', 'file']])}")

if 'response' in immune_df.columns:
    response_counts = immune_df['response'].value_counts()
    print(f"Response distribution: {response_counts.to_dict()}")

# Show expression statistics for key immune genes
key_genes = ['CD274', 'PDCD1', 'CTLA4', 'CD8A', 'FOXP3', 'GZMB']
available_key_genes = [gene for gene in key_genes if gene in immune_df.columns]

if available_key_genes:
    print("\nKey immune gene expression (log2 FPKM):")
    for gene in available_key_genes:
        mean_expr = immune_df[gene].mean()
        std_expr = immune_df[gene].std()
        print(f"  {gene}: {mean_expr:.2f} ± {std_expr:.2f}")

# Show immune scores if calculated
score_cols = [col for col in immune_df.columns if 'score' in col.lower()]
if score_cols:
    print("\nImmune signature scores:")
    for score in score_cols:
        mean_score = immune_df[score].mean()
        std_score = immune_df[score].std()
        print(f"  {score}: {mean_score:.2f} ± {std_score:.2f}")

wsi_available = (immune_df['num_patches'] != 'No WSI Data').sum()
print(f"\nWSI data availability: {wsi_available}/{len(immune_df)} samples ({wsi_available/len(immune_df)*100:.1f}%)")

print("="*50)
