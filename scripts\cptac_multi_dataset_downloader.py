import os
import requests
import pandas as pd
from pathlib import Path
import time
from urllib.parse import urljoin
import warnings
warnings.filterwarnings('ignore')

# --------- CPTAC DATASET CONFIGURATION ---------
CPTAC_DATASETS = {
    'CPTAC-GBM': {
        'name': 'Glioblastoma',
        'samples': 99,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-GBM/',
        'available': True,
        'priority': 1  # Already processed
    },
    'CPTAC-UCEC': {
        'name': 'Uterine Corpus Endometrial Carcinoma',
        'samples': 138,  # Confirmatory study
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-UCEC/',
        'available': True,
        'priority': 2
    },
    'CPTAC-LUAD': {
        'name': 'Lung Adenocarcinoma',
        'samples': 110,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-LUAD/',
        'available': True,
        'priority': 3
    },
    'CPTAC-COAD': {
        'name': 'Colon Adenocarcinoma',
        'samples': 210,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-COAD/',
        'available': True,
        'priority': 4
    },
    'CPTAC-BRCA': {
        'name': 'Breast Cancer Prospective Cohort',
        'samples': 122,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-BRCA/',
        'available': True,
        'priority': 5
    },
    'CPTAC-SARC': {
        'name': 'Soft Tissue Sarcoma',
        'samples': 173,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-SARC/',
        'available': False,  # No download permission
        'priority': 6
    },
    'CPTAC-GC': {
        'name': 'Gastric Cancer Discovery',
        'samples': 159,
        'base_url': 'https://www.linkedomics.org/data_download/CPTAC-GC/',
        'available': False,  # No download permission
        'priority': 7
    }
}

# CPTAC file patterns - each dataset has unique naming conventions
CPTAC_FILE_PATTERNS = {
    'CPTAC-GBM': {
        'clinical': 'HS_CPTAC_GBM_CLI.tsi',
        'rnaseq': 'HS_CPTAC_GBM_rnaseq_fpkm_uq_log2.cct',
        'mirna': 'HS_CPTAC_GBM_mirna_mature_tpm_log2.cct',
        'proteome': 'HS_CPTAC_GBM_proteome_mssm_per_gene.cct',
        'phosphoproteome': 'HS_CPTAC_GBM_phosphoproteome_mssm_site.cct',
        'acetylome': 'HS_CPTAC_GBM_acetylome_mssm_site.cct',
        'copy_number': 'HS_CPTAC_GBM_wgs_somatic_cnv_per_gene.cct',
        'mutation': 'somaticwrapper_all_cases_filtered.v4.0.20200430.maf.gz',
        'circular_rna': 'HS_CPTAC_GBM_circular_rna_rsem_uq_log2.cct'
    },
    'CPTAC-UCEC': {
        'clinical': 'HS_CPTAC_UCEC_CLI.txt',
        'rnaseq_tumor': 'HS_CPTAC_UCEC_RNAseq_RSEM_UQ_log2_Tumor.cct',
        'rnaseq_normal': 'HS_CPTAC_UCEC_RNAseq_RSEM_UQ_log2_Normal.cct',
        'mirna_tumor': 'HS_CPTAC_UCEC_microRNA_log2_Tumor.cct',
        'mirna_normal': 'HS_CPTAC_UCEC_microRNA_log2_Normal.cct',
        'proteome_tumor': 'HS_CPTAC_UCEC_Proteomics_TMT_gene_level_Tumor.cct',
        'proteome_normal': 'HS_CPTAC_UCEC_Proteomics_TMT_gene_level_Normal.cct',
        'phosphoproteome_tumor': 'HS_CPTAC_UCEC_Phosphoproteomics_site_level_log2_Tumor.cct',
        'phosphoproteome_normal': 'HS_CPTAC_UCEC_Phosphoproteomics_site_level_log2_Normal.cct',
        'acetylome_tumor': 'HS_CPTAC_UCEC_Acetylproteomics_site_level_log2_Tumor.cct',
        'acetylome_normal': 'HS_CPTAC_UCEC_Acetylproteomics_site_level_log2_Normal.cct',
        'copy_number': 'HS_CPTAC_UCEC_SCNA_log2_gene_level.cct',
        'mutation_gene': 'HS_CPTAC_UCEC_SomaticMutations_gene_level.cbt',
        'mutation_site': 'HS_CPTAC_UCEC_SomaticMutations_site_level.cbt',
        'mutation_maf': 'UCEC_somatic_mutation_site_level_V2.1.maf',
        'circular_rna_tumor': 'HS_CPTAC_UCEC_circRNAseq_RSEM_UQ_log2_Tumor.cct',
        'circular_rna_normal': 'HS_CPTAC_UCEC_circRNAseq_RSEM_UQ_log2_Normal.cct',
        'methylation': 'HS_CPTAC_UCEC_Methylation_betaValue_gene_level_mean.cct'
    }
}

# --------- USER CONFIGURATION ---------
BASE_DATA_DIR = "data"
DOWNLOAD_TIMEOUT = 300  # 5 minutes per file
MAX_RETRIES = 3
DELAY_BETWEEN_DOWNLOADS = 2  # seconds
DATASETS_TO_PROCESS = ['CPTAC-UCEC', 'CPTAC-LUAD', 'CPTAC-COAD', 'CPTAC-BRCA']  # Skip GBM (already done)
# --------------------------------------

def create_directory_structure(dataset_id):
    """Create directory structure for a CPTAC dataset"""
    dataset_dir = Path(BASE_DATA_DIR) / dataset_id.lower().replace('-', '_')
    raw_data_dir = dataset_dir / "raw_data"
    processed_data_dir = dataset_dir / "processed_data"
    
    raw_data_dir.mkdir(parents=True, exist_ok=True)
    processed_data_dir.mkdir(parents=True, exist_ok=True)
    
    return dataset_dir, raw_data_dir, processed_data_dir

def download_file(url, local_path, max_retries=MAX_RETRIES):
    """Download a file with retry logic"""
    for attempt in range(max_retries):
        try:
            print(f"   Downloading: {url}")
            print(f"   Attempt {attempt + 1}/{max_retries}")
            
            response = requests.get(url, timeout=DOWNLOAD_TIMEOUT, stream=True)
            response.raise_for_status()
            
            # Get file size if available
            total_size = int(response.headers.get('content-length', 0))
            
            with open(local_path, 'wb') as f:
                downloaded = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded += len(chunk)
                        
                        # Show progress for large files
                        if total_size > 0 and downloaded % (1024*1024) == 0:  # Every MB
                            progress = (downloaded / total_size) * 100
                            print(f"   Progress: {progress:.1f}% ({downloaded/1024/1024:.1f} MB)")
            
            file_size = os.path.getsize(local_path)
            print(f"   Downloaded: {local_path.name} ({file_size/1024/1024:.1f} MB)")
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"   Error downloading {url}: {e}")
            if attempt < max_retries - 1:
                print(f"   Retrying in {DELAY_BETWEEN_DOWNLOADS} seconds...")
                time.sleep(DELAY_BETWEEN_DOWNLOADS)
            else:
                print(f"   Failed to download after {max_retries} attempts")
                return False
        except Exception as e:
            print(f"   Unexpected error: {e}")
            return False
    
    return False

def download_cptac_dataset(dataset_id):
    """Download all available files for a CPTAC dataset"""
    if dataset_id not in CPTAC_DATASETS:
        print(f"ERROR: Unknown dataset {dataset_id}")
        return False
    
    dataset_info = CPTAC_DATASETS[dataset_id]
    
    if not dataset_info['available']:
        print(f"SKIPPING: {dataset_id} - No download permission available")
        return False
    
    print(f"\n{'='*60}")
    print(f"DOWNLOADING: {dataset_id}")
    print(f"Cancer Type: {dataset_info['name']}")
    print(f"Samples: {dataset_info['samples']}")
    print(f"{'='*60}")
    
    # Create directory structure
    dataset_dir, raw_data_dir, processed_data_dir = create_directory_structure(dataset_id)

    downloaded_files = []
    failed_files = []

    # Get file patterns for this specific dataset
    if dataset_id not in CPTAC_FILE_PATTERNS:
        print(f"   ERROR: No file patterns defined for {dataset_id}")
        return False

    file_patterns = CPTAC_FILE_PATTERNS[dataset_id]

    # Try to download each file type
    for file_type, filename in file_patterns.items():
        file_url = urljoin(dataset_info['base_url'], filename)
        local_path = raw_data_dir / filename

        print(f"\n--- {file_type.upper()} DATA ---")

        # Skip if file already exists and is not empty
        if local_path.exists() and local_path.stat().st_size > 0:
            print(f"   File already exists: {filename}")
            downloaded_files.append((file_type, filename, local_path.stat().st_size))
            continue

        # Attempt download
        if download_file(file_url, local_path):
            file_size = local_path.stat().st_size
            downloaded_files.append((file_type, filename, file_size))
            time.sleep(DELAY_BETWEEN_DOWNLOADS)  # Be respectful to server
        else:
            failed_files.append((file_type, filename))
            # Remove empty file if download failed
            if local_path.exists() and local_path.stat().st_size == 0:
                local_path.unlink()
    
    # Summary
    print(f"\n{'='*60}")
    print(f"DOWNLOAD SUMMARY: {dataset_id}")
    print(f"{'='*60}")
    print(f"Successfully downloaded: {len(downloaded_files)} files")
    
    total_size = 0
    for file_type, filename, size in downloaded_files:
        print(f"  ✓ {file_type}: {filename} ({size/1024/1024:.1f} MB)")
        total_size += size
    
    print(f"\nTotal downloaded: {total_size/1024/1024:.1f} MB")
    
    if failed_files:
        print(f"\nFailed downloads: {len(failed_files)} files")
        for file_type, filename in failed_files:
            print(f"  ✗ {file_type}: {filename}")
    
    # Save download log
    log_file = dataset_dir / "download_log.txt"
    with open(log_file, 'w') as f:
        f.write(f"CPTAC Dataset Download Log: {dataset_id}\n")
        f.write(f"Download Date: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Cancer Type: {dataset_info['name']}\n")
        f.write(f"Expected Samples: {dataset_info['samples']}\n\n")
        
        f.write("Successfully Downloaded Files:\n")
        for file_type, filename, size in downloaded_files:
            f.write(f"  {file_type}: {filename} ({size/1024/1024:.1f} MB)\n")
        
        if failed_files:
            f.write("\nFailed Downloads:\n")
            for file_type, filename in failed_files:
                f.write(f"  {file_type}: {filename}\n")
        
        f.write(f"\nTotal Size: {total_size/1024/1024:.1f} MB\n")
    
    return len(downloaded_files) > 0

def main():
    """Main function to download multiple CPTAC datasets"""
    print("="*80)
    print("CPTAC MULTI-DATASET DOWNLOADER")
    print("="*80)
    
    # Show available datasets
    print("\nAvailable CPTAC Datasets:")
    for dataset_id, info in CPTAC_DATASETS.items():
        status = "✓ Available" if info['available'] else "✗ No Permission"
        print(f"  {dataset_id}: {info['name']} ({info['samples']} samples) - {status}")
    
    print(f"\nDatasets to process: {DATASETS_TO_PROCESS}")
    
    # Download each dataset
    successful_downloads = []
    failed_downloads = []
    
    for dataset_id in DATASETS_TO_PROCESS:
        if download_cptac_dataset(dataset_id):
            successful_downloads.append(dataset_id)
        else:
            failed_downloads.append(dataset_id)
    
    # Final summary
    print("\n" + "="*80)
    print("MULTI-DATASET DOWNLOAD COMPLETE")
    print("="*80)
    
    print(f"Successfully processed: {len(successful_downloads)} datasets")
    for dataset_id in successful_downloads:
        print(f"  ✓ {dataset_id}")
    
    if failed_downloads:
        print(f"\nFailed to process: {len(failed_downloads)} datasets")
        for dataset_id in failed_downloads:
            print(f"  ✗ {dataset_id}")
    
    print(f"\nNext steps:")
    print("1. Verify downloaded data integrity")
    print("2. Create universal processing scripts")
    print("3. Process each dataset with adapted scripts")
    print("4. Perform pan-CPTAC integration analysis")

if __name__ == "__main__":
    main()
