#!/usr/bin/env python3
"""
Pan-CPTAC Multi-Cancer Integration Analysis

This script integrates immune signatures across multiple CPTAC cancer types
for comparative oncology insights and cross-cancer analysis.

Author: AI Assistant
Date: 2025-01-02
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
import logging
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PanCPTACIntegrator:
    """Pan-CPTAC multi-cancer integration analysis"""
    
    def __init__(self, base_data_dir: str = "data"):
        self.base_data_dir = Path(base_data_dir)
        self.output_dir = Path("results/pan_cptac_analysis")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # CPTAC datasets
        self.datasets = ['CPTAC-GBM', 'CPTAC-UCEC', 'CPTAC-LUAD', 'CPTAC-COAD', 'CPTAC-BRCA']
        
        # Initialize data containers
        self.clinical_data = {}
        self.rnaseq_data = {}
        self.proteome_data = {}
        
        # Cancer type mapping
        self.cancer_type_map = {
            'CPTAC-GBM': 'Glioblastoma',
            'CPTAC-UCEC': 'Endometrial Carcinoma',
            'CPTAC-LUAD': 'Lung Adenocarcinoma',
            'CPTAC-COAD': 'Colon Adenocarcinoma',
            'CPTAC-BRCA': 'Breast Cancer'
        }
    
    def load_processed_data(self):
        """Load all processed CPTAC datasets"""
        logger.info("Loading processed CPTAC datasets...")
        
        for dataset_id in self.datasets:
            cancer_type = dataset_id.split('-')[1].lower()
            dataset_dir = self.base_data_dir / f"cptac_{cancer_type}" / "processed_data"
            
            # Load clinical data
            clinical_file = dataset_dir / f"{dataset_id.lower()}_clinical_processed.csv"
            if clinical_file.exists():
                self.clinical_data[dataset_id] = pd.read_csv(clinical_file, index_col=0)
                logger.info(f"Loaded {dataset_id} clinical: {self.clinical_data[dataset_id].shape}")
            
            # Load RNA-seq data (tumor samples)
            rnaseq_file = dataset_dir / f"{dataset_id.lower()}_rnaseq_processed.csv"
            if rnaseq_file.exists():
                self.rnaseq_data[dataset_id] = pd.read_csv(rnaseq_file, index_col=0)
                logger.info(f"Loaded {dataset_id} RNA-seq: {self.rnaseq_data[dataset_id].shape}")
            
            # Load proteome data (tumor samples)
            proteome_file = dataset_dir / f"{dataset_id.lower()}_proteome_processed.csv"
            if proteome_file.exists():
                self.proteome_data[dataset_id] = pd.read_csv(proteome_file, index_col=0)
                logger.info(f"Loaded {dataset_id} proteome: {self.proteome_data[dataset_id].shape}")
    
    def create_integrated_datasets(self):
        """Create integrated datasets across cancer types"""
        logger.info("Creating integrated datasets...")
        
        # Integrate RNA-seq data
        if self.rnaseq_data:
            # Find common genes across all datasets
            common_genes = set(self.rnaseq_data[list(self.rnaseq_data.keys())[0]].columns)
            for dataset_id, data in self.rnaseq_data.items():
                common_genes = common_genes.intersection(set(data.columns))
            
            logger.info(f"Found {len(common_genes)} common genes across RNA-seq datasets")
            
            # Create integrated RNA-seq dataset
            integrated_rnaseq = []
            for dataset_id, data in self.rnaseq_data.items():
                subset = data[list(common_genes)].copy()
                subset['cancer_type'] = self.cancer_type_map[dataset_id]
                subset['dataset_id'] = dataset_id
                integrated_rnaseq.append(subset)
            
            self.integrated_rnaseq = pd.concat(integrated_rnaseq, axis=0)
            logger.info(f"Integrated RNA-seq dataset: {self.integrated_rnaseq.shape}")
        
        # Integrate proteome data
        if self.proteome_data:
            # Find common proteins across all datasets
            common_proteins = set(self.proteome_data[list(self.proteome_data.keys())[0]].columns)
            for dataset_id, data in self.proteome_data.items():
                common_proteins = common_proteins.intersection(set(data.columns))
            
            logger.info(f"Found {len(common_proteins)} common proteins across proteome datasets")
            
            # Create integrated proteome dataset
            integrated_proteome = []
            for dataset_id, data in self.proteome_data.items():
                subset = data[list(common_proteins)].copy()
                subset['cancer_type'] = self.cancer_type_map[dataset_id]
                subset['dataset_id'] = dataset_id
                integrated_proteome.append(subset)
            
            self.integrated_proteome = pd.concat(integrated_proteome, axis=0)
            logger.info(f"Integrated proteome dataset: {self.integrated_proteome.shape}")
    
    def perform_cross_cancer_analysis(self):
        """Perform cross-cancer comparative analysis"""
        logger.info("Performing cross-cancer analysis...")
        
        results = {}
        
        # RNA-seq analysis
        if hasattr(self, 'integrated_rnaseq'):
            results['rnaseq'] = self._analyze_omics_data(
                self.integrated_rnaseq, 
                'RNA-seq', 
                'Gene Expression'
            )
        
        # Proteome analysis
        if hasattr(self, 'integrated_proteome'):
            results['proteome'] = self._analyze_omics_data(
                self.integrated_proteome, 
                'Proteome', 
                'Protein Expression'
            )
        
        return results
    
    def _analyze_omics_data(self, data, data_type, ylabel):
        """Analyze integrated omics data"""
        logger.info(f"Analyzing {data_type} data...")
        
        # Separate features from metadata
        feature_cols = [col for col in data.columns if col not in ['cancer_type', 'dataset_id']]
        features = data[feature_cols]
        metadata = data[['cancer_type', 'dataset_id']]
        
        # Remove features with too many missing values
        features = features.dropna(axis=1, thresh=len(features) * 0.5)
        
        # Fill remaining missing values with median
        features = features.fillna(features.median())
        
        logger.info(f"Using {len(feature_cols)} features for {data_type} analysis")
        
        # Standardize features
        scaler = StandardScaler()
        features_scaled = pd.DataFrame(
            scaler.fit_transform(features),
            index=features.index,
            columns=features.columns
        )
        
        # PCA analysis
        pca = PCA(n_components=min(10, features_scaled.shape[1]))
        pca_result = pca.fit_transform(features_scaled)
        
        # Create PCA DataFrame
        pca_df = pd.DataFrame(
            pca_result,
            index=features_scaled.index,
            columns=[f'PC{i+1}' for i in range(pca_result.shape[1])]
        )
        pca_df = pd.concat([pca_df, metadata], axis=1)
        
        # Statistical analysis - compare cancer types
        stats_results = self._compare_cancer_types(features_scaled, metadata)
        
        # Visualization
        self._create_visualizations(pca_df, features_scaled, metadata, data_type, ylabel)
        
        return {
            'pca_df': pca_df,
            'pca_explained_variance': pca.explained_variance_ratio_,
            'features_scaled': features_scaled,
            'stats_results': stats_results
        }
    
    def _compare_cancer_types(self, features, metadata):
        """Statistical comparison between cancer types"""
        logger.info("Performing statistical comparisons between cancer types...")
        
        results = {}
        cancer_types = metadata['cancer_type'].unique()
        
        # For each feature, perform ANOVA across cancer types
        significant_features = []
        
        for feature in features.columns:
            groups = [features.loc[metadata['cancer_type'] == ct, feature].dropna() 
                     for ct in cancer_types]
            
            # Remove empty groups
            groups = [g for g in groups if len(g) > 0]
            
            if len(groups) >= 2:
                try:
                    f_stat, p_value = stats.f_oneway(*groups)
                    if p_value < 0.05:
                        significant_features.append({
                            'feature': feature,
                            'f_statistic': f_stat,
                            'p_value': p_value
                        })
                except:
                    continue
        
        results['significant_features'] = pd.DataFrame(significant_features)
        results['n_significant'] = len(significant_features)
        
        logger.info(f"Found {len(significant_features)} significantly different features across cancer types")
        
        return results
    
    def _create_visualizations(self, pca_df, features_scaled, metadata, data_type, ylabel):
        """Create visualizations for cross-cancer analysis"""
        logger.info(f"Creating visualizations for {data_type}...")
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_palette("husl")
        
        # 1. PCA plot
        plt.figure(figsize=(12, 8))
        
        # PCA scatter plot
        plt.subplot(2, 2, 1)
        for cancer_type in pca_df['cancer_type'].unique():
            subset = pca_df[pca_df['cancer_type'] == cancer_type]
            plt.scatter(subset['PC1'], subset['PC2'], 
                       label=cancer_type, alpha=0.7, s=50)
        
        plt.xlabel(f'PC1 ({pca_df.iloc[:, 0].var():.1%} variance)')
        plt.ylabel(f'PC2 ({pca_df.iloc[:, 1].var():.1%} variance)')
        plt.title(f'{data_type}: PCA Analysis')
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        
        # 2. Sample count by cancer type
        plt.subplot(2, 2, 2)
        cancer_counts = metadata['cancer_type'].value_counts()
        plt.bar(range(len(cancer_counts)), cancer_counts.values)
        plt.xticks(range(len(cancer_counts)), cancer_counts.index, rotation=45)
        plt.title(f'{data_type}: Sample Counts by Cancer Type')
        plt.ylabel('Number of Samples')
        
        # 3. Feature variance across cancer types
        plt.subplot(2, 2, 3)
        feature_vars = features_scaled.var(axis=0).sort_values(ascending=False)
        plt.plot(range(len(feature_vars)), feature_vars.values)
        plt.xlabel('Feature Rank')
        plt.ylabel('Variance')
        plt.title(f'{data_type}: Feature Variance Distribution')
        plt.grid(True, alpha=0.3)
        
        # 4. Heatmap of top variable features by cancer type
        plt.subplot(2, 2, 4)
        top_features = feature_vars.head(10).index
        heatmap_data = []
        
        for cancer_type in metadata['cancer_type'].unique():
            cancer_samples = metadata[metadata['cancer_type'] == cancer_type].index
            cancer_features = features_scaled.loc[cancer_samples, top_features]
            heatmap_data.append(cancer_features.mean())
        
        heatmap_df = pd.DataFrame(heatmap_data, index=metadata['cancer_type'].unique())
        
        sns.heatmap(heatmap_df.T, annot=True, cmap='RdBu_r', center=0, 
                   fmt='.2f', cbar_kws={'label': 'Mean Expression'})
        plt.title(f'{data_type}: Top Variable Features by Cancer Type')
        plt.xlabel('Cancer Type')
        plt.ylabel('Features')
        
        plt.tight_layout()
        
        # Save the plot
        output_file = self.output_dir / f"pan_cptac_{data_type.lower()}_analysis.png"
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        logger.info(f"Saved {data_type} visualization: {output_file}")
    
    def generate_summary_report(self, results):
        """Generate a comprehensive summary report"""
        logger.info("Generating summary report...")
        
        report_file = self.output_dir / "pan_cptac_summary_report.txt"
        
        with open(report_file, 'w') as f:
            f.write("Pan-CPTAC Multi-Cancer Integration Analysis Report\n")
            f.write("=" * 60 + "\n\n")
            
            # Dataset summary
            f.write("Dataset Summary:\n")
            f.write("-" * 20 + "\n")
            for dataset_id in self.datasets:
                cancer_type = self.cancer_type_map[dataset_id]
                f.write(f"{dataset_id}: {cancer_type}\n")
                
                if dataset_id in self.clinical_data:
                    f.write(f"  Clinical samples: {len(self.clinical_data[dataset_id])}\n")
                if dataset_id in self.rnaseq_data:
                    f.write(f"  RNA-seq samples: {len(self.rnaseq_data[dataset_id])}\n")
                if dataset_id in self.proteome_data:
                    f.write(f"  Proteome samples: {len(self.proteome_data[dataset_id])}\n")
                f.write("\n")
            
            # Integration results
            f.write("Integration Results:\n")
            f.write("-" * 20 + "\n")
            
            if hasattr(self, 'integrated_rnaseq'):
                f.write(f"Integrated RNA-seq dataset: {self.integrated_rnaseq.shape}\n")
                f.write(f"Common genes: {self.integrated_rnaseq.shape[1] - 2}\n")  # -2 for metadata cols
            
            if hasattr(self, 'integrated_proteome'):
                f.write(f"Integrated proteome dataset: {self.integrated_proteome.shape}\n")
                f.write(f"Common proteins: {self.integrated_proteome.shape[1] - 2}\n")  # -2 for metadata cols
            
            f.write("\n")
            
            # Analysis results
            f.write("Analysis Results:\n")
            f.write("-" * 20 + "\n")
            
            for data_type, result in results.items():
                f.write(f"\n{data_type.upper()} Analysis:\n")
                
                if 'pca_explained_variance' in result:
                    f.write(f"  PCA explained variance (first 3 PCs): ")
                    f.write(f"{result['pca_explained_variance'][:3].sum():.1%}\n")
                
                if 'stats_results' in result:
                    n_sig = result['stats_results']['n_significant']
                    f.write(f"  Significantly different features across cancer types: {n_sig}\n")
        
        logger.info(f"Summary report saved: {report_file}")

def main():
    """Main integration analysis function"""
    logger.info("Starting Pan-CPTAC integration analysis...")
    
    # Initialize integrator
    integrator = PanCPTACIntegrator()
    
    # Load processed data
    integrator.load_processed_data()
    
    # Create integrated datasets
    integrator.create_integrated_datasets()
    
    # Perform cross-cancer analysis
    results = integrator.perform_cross_cancer_analysis()
    
    # Generate summary report
    integrator.generate_summary_report(results)
    
    logger.info("Pan-CPTAC integration analysis complete!")
    logger.info(f"Results saved in: {integrator.output_dir}")

if __name__ == "__main__":
    main()
